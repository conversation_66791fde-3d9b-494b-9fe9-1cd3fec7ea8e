name: market
description: A new Flutter project.
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: '>=3.1.0 <4.0.0'

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter


  # The following adds the Cupertino Icons font to your application.
  # Use with the CupertinoIcons class for iOS style icons.
  cupertino_icons: ^1.0.2
  firebase_core: ^2.24.2
  cloud_firestore: ^4.13.6
  badges: ^3.1.2
  pattern_formatter: ^4.0.0
  intl: ^0.18.1
  flutter_slidable: ^3.0.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0
  dotted_dashed_line: ^0.0.3
  internet_connection_checker: ^1.0.0+1
#  flutter_pos_printer_platform_image_3: ^1.2.4
  esc_pos_utils_plus: ^2.0.2
  textfield_search: ^0.11.1
  custom_pin_screen: ^1.0.1
  fluttertoast:
  change_app_package_name: ^1.5.0
  get_storage: ^2.1.1
  top_snackbar_flutter: ^3.1.0
  dropdown_button2: ^2.3.9
  math_expressions: ^2.6.0
  equatable: ^2.0.5
  flutter_bloc: ^8.1.4
  firebase_pagination: 3.1.0
  win32: ^5.5.0




dependency_overrides:
  intl: ^0.19.0
  flutter_pos_printer_platform_image_3: ^1.1.2

dev_dependencies:
  flutter_test:
    sdk: flutter

  # The "flutter_lints" package below contains a set of recommended lints to
  # encourage good coding practices. The lint set provided by the package is
  # activated in the `analysis_options.yaml` file located at the root of your
  # package. See that file for information about deactivating specific lint
  # rules and activating additional ones.
  flutter_lints: ^2.0.0
  build_runner: ^2.4.6
  hive_generator: ^2.0.1
  icons_launcher: ^2.1.4
  rename: ^3.0.0
  flutter_launcher_icons:


flutter_launcher_icons:
  android: "launcher_icon"
  ios: true
  image_path: "assets/sale.png"
  min_sdk_android: 21

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # To add assets to your application, add an assets section, like this:
  assets:
    - assets/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/assets-and-images/#resolution-aware

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/assets-and-images/#from-packages

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  fonts:
    - family: Inter
      fonts:
        - asset: assets/fonts/Inter-Bold.ttf
        - asset: assets/fonts/Inter-Medium.ttf
        - asset: assets/fonts/Inter-SemiBold.ttf
        - asset: assets/fonts/Inter-Regular.ttf

    - family: Montserrat
      fonts:
        - asset: assets/fonts/OpenSans-Regular.ttf
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/custom-fonts/#from-packages
