import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_pagination/firebase_pagination.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/load_page.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'check_detail.dart';

class CheckPage extends StatefulWidget {
  const CheckPage({super.key});

  @override
  State<CheckPage> createState() => _CheckPageState();
}

class _CheckPageState extends State<CheckPage> {
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd');
  String? date;
  bool isDateSearch = false;
  TextEditingController searchController = TextEditingController();
  final CollectionReference tradeHistory =
      FirebaseFirestore.instance.collection("trade_history");

  Query<Object?> searchName(String searchText) {
    return FirebaseFirestore.instance
        .collection('trade_history')
        .where('name', arrayContains: searchText);
  }

  Query<Object?> searchDate(String? date) {
    if (date != null) {
      return FirebaseFirestore.instance
          .collection('trade_history')
          .where('date', isEqualTo: date);
    } else {
      return FirebaseFirestore.instance
          .collection('trade_history')
          .orderBy('date', descending: true);
    }
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Savdo bo\'limi',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Savdo tarixi va hisobotlar',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Column(
        children: [
          _buildModernSearchField(),
          Expanded(
              child: FirestorePagination(
            isLive: true,
            onEmpty: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Container(
                    padding: const EdgeInsets.all(24),
                    decoration: BoxDecoration(
                      color: cItemColor.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Icon(
                      Icons.receipt_long_outlined,
                      size: 64,
                      color: cGray,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Savdo tarixi bo\'sh',
                    style: TextStyle(
                      color: cWhite,
                      fontSize: 20,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Hali hech qanday savdo operatsiyasi amalga oshirilmagan',
                    style: TextStyle(
                      color: cGray,
                      fontSize: 14,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
            key: ValueKey('$isDateSearch-${searchController.text}-$date'),
            query: isDateSearch
                ? searchName(searchController.text)
                : searchDate(date),
            itemBuilder: (context, documentSnapshot, index) {
              final DocumentSnapshot snapshot = documentSnapshot;
              List<dynamic> names = snapshot['name'];
              bool isSold = snapshot['sold'];
              return Slidable(
                key: const ValueKey(1),
                endActionPane:
                    ActionPane(motion: const StretchMotion(), children: [
                  SlidableAction(
                    onPressed: (BuildContext context) {
                      showDialog(
                          context: context,
                          builder: (context) {
                            return AlertDialog(
                              backgroundColor: cItemColor,
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(16),
                              ),
                              title: const Text(
                                "Ogohlantirish",
                                style: TextStyle(
                                  color: cWhite,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              content: const Text(
                                "Rostan ham ushbu savdo ma'lumotini o'chirmoqchimisiz?",
                                style: TextStyle(color: cGray),
                              ),
                              actions: [
                                TextButton(
                                  onPressed: () => Navigator.pop(context),
                                  child: const Text(
                                    "Yo'q",
                                    style: TextStyle(color: cGray),
                                  ),
                                ),
                                Container(
                                  decoration: BoxDecoration(
                                    color: cRed,
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                  child: TextButton(
                                    onPressed: () async {
                                      bool result = await InternetConnectionChecker().hasConnection;
                                      if (result) {
                                        deleteTradeHistoryWithSubcollections(snapshot.id);
                                        Navigator.pop(context);
                                      } else {
                                        showTopSnackBar(
                                          Overlay.of(context),
                                          CustomSnackBar.error(
                                            message: "Ma'lumotlarni o'chirish uchun internet zarur",
                                          ),
                                        );
                                      }
                                    },
                                    child: const Text(
                                      "Ha",
                                      style: TextStyle(color: cWhite),
                                    ),
                                  ),
                                ),
                              ],
                            );
                          });
                    },
                    icon: Icons.delete,
                    backgroundColor: Colors.red,
                  ),
                ]),
                child: GestureDetector(
                  onTap: () {
                    Navigator.push(
                        context,
                        MaterialPageRoute(
                            builder: (_) => CheckDetail(
                                  documentSnapshot: snapshot,
                                )));
                  },
                  child: Container(
                    margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 6),
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(16),
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: isSold
                            ? [Colors.green[800]!, Colors.green[700]!]
                            : [cItemColor, cItemColor.withOpacity(0.8)],
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: (isSold ? Colors.green : cButtonColor).withOpacity(0.1),
                          blurRadius: 10,
                          offset: const Offset(0, 4),
                        ),
                      ],
                      border: Border.all(
                        color: (isSold ? Colors.green : cButtonColor).withOpacity(0.3),
                        width: 1,
                      ),
                    ),
                    child: ListTile(
                      contentPadding: const EdgeInsets.all(16),
                      leading: Container(
                        padding: const EdgeInsets.all(8),
                        decoration: BoxDecoration(
                          color: (isSold ? Colors.green : cButtonColor).withOpacity(0.2),
                          borderRadius: BorderRadius.circular(10),
                          border: Border.all(
                            color: (isSold ? Colors.green : cButtonColor).withOpacity(0.4),
                            width: 1,
                          ),
                        ),
                        child: Icon(
                          isSold ? Icons.check_circle_outline : Icons.receipt_long_outlined,
                          color: isSold ? Colors.white : cButtonColor,
                          size: 20,
                        ),
                      ),
                      title: Text(
                        names.isNotEmpty ? capitalFirst(names.last) : "Noma'lum",
                        style: TextStyle(
                          color: cWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      subtitle: Row(
                        children: [
                          Icon(
                            Icons.calendar_today_outlined,
                            color: Colors.white,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            formatterDate.format(
                              DateTime.parse(snapshot['date']),
                            ),
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                      trailing: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                        decoration: BoxDecoration(
                          color: isSold
                              ? Colors.green.withOpacity(0.2)
                              : cYellow.withOpacity(0.2),
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(
                            color: isSold
                                ? Colors.green.withOpacity(0.4)
                                : cYellow.withOpacity(0.4),
                            width: 1,
                          ),
                        ),
                        child: Text(
                          isSold ? 'Sotilgan' : 'Kutilmoqda',
                          style: TextStyle(
                            color: isSold ? Colors.white : cYellow,
                            fontSize: 10,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
              );
            },
          )),
        ],
    );
  }


  Future<void> deleteTradeHistoryWithSubcollections(String id) async {
    try {
      // Reference to the document
      final DocumentReference tempDocRef = tradeHistory.doc(id);

      // Since we know we have an 'actions' subcollection, we'll directly reference it
      final CollectionReference actionsRef = tempDocRef.collection('actions');

      // Get all documents in the actions subcollection
      final QuerySnapshot snapshot = await actionsRef.get();

      // If there are documents to delete
      if (snapshot.docs.isNotEmpty) {
        // Create a batch for deletion
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Add delete operations for each document
        for (var doc in snapshot.docs) {
          batch.delete(doc.reference);
        }

        // Commit the batch
        await batch.commit();
      }

      // Finally delete the document itself
      await tempDocRef.delete();

      print('Document and all subcollections deleted successfully');
    } catch (e) {
      print('Error during deletion: $e');
    }
  }



// Future<void> deleteTradeHistoryWithSubcollections(String id) async {
  //   try {
  //     // Reference to the main document
  //     final DocumentReference docRef =instance.collection('trade_history').doc(id);
  //
  //     // Reference to the 'actions' subcollection
  //     final CollectionReference actionsRef = docRef.collection('actions');
  //
  //     // Delete all documents inside 'actions' subcollection
  //     await deleteCollection(actionsRef);
  //
  //     // Now delete the parent document
  //     await docRef.delete();
  //
  //     print('Document and its subcollection deleted successfully');
  //   } catch (e) {
  //     print('Error during deletion: $e');
  //   }
  // }

  // Future<void> deleteCollection(CollectionReference collectionRef) async {
  //   try {
  //     // Get all documents in the collection
  //     final QuerySnapshot snapshot = await collectionRef.get();
  //
  //     for (var doc in snapshot.docs) {
  //       // Delete each document inside 'actions'
  //       await doc.reference.delete();
  //     }
  //   } catch (e) {
  //     print('Error deleting collection: $e');
  //   }
  // }

  Widget _buildModernSearchField() {
    return Container(
      margin: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cItemColor.withOpacity(0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: searchController,
        style: TextStyle(color: cWhite),
        decoration: InputDecoration(
          hintText: date != null ? "Sana: $date" : "Klient nomini kiriting yoki sana tanlang",
          hintStyle: TextStyle(
            color: date != null ? cWhite : cGray,
            fontWeight: date != null ? FontWeight.w500 : FontWeight.normal,
          ),
          prefixIcon: Icon(
            date != null ? Icons.calendar_today : Icons.search,
            color: date != null ? Colors.green : cButtonColor,
          ),
          suffixIcon: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              if (date != null || searchController.text.isNotEmpty)
                GestureDetector(
                  onTap: () {
                    setState(() {
                      searchController.clear();
                      date = null;
                      isDateSearch = false;
                    });
                  },
                  child: Container(
                    margin: const EdgeInsets.only(right: 8),
                    padding: const EdgeInsets.all(6),
                    decoration: BoxDecoration(
                      color: Colors.red.withOpacity(0.2),
                      borderRadius: BorderRadius.circular(6),
                    ),
                    child: Icon(
                      Icons.clear,
                      color: Colors.red,
                      size: 14,
                    ),
                  ),
                ),
              GestureDetector(
                onTap: () {
                  showDatePicker(
                    context: context,
                    initialDate: date != null
                        ? DateTime.parse(date!)
                        : DateTime.now(),
                    firstDate: DateTime(2023),
                    lastDate: DateTime(2099),
                  ).then((value) {
                    if (value != null) {
                      setState(() {
                        isDateSearch = false;
                        searchController.clear();
                        date = formatterDate.format(value);
                        // Update the search controller to show the selected date
                        searchController.text = "📅 ${_formatDisplayDate(value)}";
                      });
                    }
                  });
                },
                child: Container(
                  margin: const EdgeInsets.all(8),
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: date != null
                        ? Colors.green.withOpacity(0.2)
                        : cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: date != null
                          ? Colors.green.withOpacity(0.4)
                          : cButtonColor.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: Icon(
                    Icons.calendar_month_outlined,
                    color: date != null ? Colors.green : cButtonColor,
                    size: 16,
                  ),
                ),
              ),
            ],
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        onChanged: (value) {
          // Don't allow manual editing when date is selected
          if (date != null) {
            return;
          }
          setState(() {
            date = null;
            if (value.isEmpty) {
              isDateSearch = false;
            } else {
              isDateSearch = true;
            }
          });
        },
        readOnly: date != null, // Make read-only when date is selected
      ),
    );
  }

  String _formatDisplayDate(DateTime date) {
    final DateFormat displayFormatter = DateFormat('dd.MM.yyyy');
    return displayFormatter.format(date);
  }
}
