import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/print_page.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'main.dart';
import 'model/product.dart';

class CheckDetail extends StatefulWidget {
  final DocumentSnapshot documentSnapshot;

  const CheckDetail({super.key, required this.documentSnapshot});

  @override
  State<CheckDetail> createState() => _CheckDetailState();
}

class _CheckDetailState extends State<CheckDetail> {
  final DateFormat formatterDate = DateFormat('dd-MM-yyyy'); //HH:mm
  final CollectionReference tradeHistory =
      FirebaseFirestore.instance.collection("trade_history");
  final CollectionReference _product =
      FirebaseFirestore.instance.collection("products");
  List<dynamic> names = [];

  @override
  void initState() {
    super.initState();
    names = widget.documentSnapshot['name'];
  }

  void fetchProductDetails(
      {required String productId, required int count}) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> snapshot = await _product
          .doc(productId)
          .get() as DocumentSnapshot<Map<String, dynamic>>;

      if (snapshot.exists) {
        int baseCount = snapshot.data()?["count"];
        int newCount = baseCount - count;

        // Ensure baseCount never goes below zero
        if (newCount < 0) {
          newCount = 0;
          print('Warning: Attempted to reduce stock below zero for product $productId. Setting count to 0.');
        }

        _product.doc(productId).update({"count": newCount});
      } else {
        print('Document with ID $productId does not exist.');
      }
    } catch (e) {
      print('Error fetching product details: $e');
    }
  }

  FullInfoProduct buildList(
      AsyncSnapshot<QuerySnapshot<Object?>> snapshot, String name) {
    int totalSum = 0;
    try {
      print(snapshot.data);
      List<Product>? list = snapshot.data?.docs.map((doc) {
        totalSum = totalSum +
            ((doc['price'] ?? 0) as int) * ((doc['count'] ?? 0) as int);
        return Product(
          id: doc.id,
          name: doc['name'] ?? 'Unknown',
          price: (doc['price'] ?? 0) as int,
          count: (doc['count'] ?? 0) as int,
          productId: doc['product_id'],
        );
      }).toList();
      print(totalSum);
      print(list);
      return FullInfoProduct(name: name, list: list, totalSum: totalSum);

      // Refresh UI after fetching data
    } catch (e) {
      print("Error fetching products: $e");
      throw Exception(e);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return StreamBuilder(
      stream: tradeHistory
          .doc(widget.documentSnapshot.id)
          .collection('actions')
          .snapshots(),
      builder: (BuildContext context,
          AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
        if (snapshot.connectionState == ConnectionState.done ||
            snapshot.connectionState == ConnectionState.active) {
          int totalPrice = 0;
          if (snapshot.hasData) {
            for (var doc in snapshot.data!.docs) {
              int price = doc['price'] ?? 0;
              int count = doc['count'] ?? 0;
              totalPrice += price * count;
            }
          }
          return Column(
            children: [
              // _buildDateHeader(),
              Expanded(
                child: _buildProductsList(snapshot),
              ),
              _buildTotalSection(totalPrice),
              _buildActionButtons(snapshot),
            ],
          );
        }
        return const Center(
          child: CircularProgressIndicator(
            color: cButtonColor,
          ),
        );
      },
    );
  }

  Widget _buildDateHeader() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16,vertical: 4),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: cButtonColor.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.calendar_today_outlined,
              color: cButtonColor,
              size: 18,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Savdo sanasi',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  formatterDate.format(
                    DateTime.parse(widget.documentSnapshot['date']),
                  ),
                  style: const TextStyle(
                    color: cWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProductsList(AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
    if (snapshot.data?.docs.isEmpty ?? true) {
      return _buildEmptyState();
    }

    return ListView.builder(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      itemCount: snapshot.data?.docs.length,
      itemBuilder: (context, index) {
        return _buildModernProductCard(snapshot, index);
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.inventory_2_outlined,
              size: 64,
              color: cGray,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Mahsulotlar topilmadi',
            style: TextStyle(
              color: cWhite,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Bu savdoda hech qanday mahsulot yo\'q',
            style: TextStyle(
              color: cGray,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernProductCard(AsyncSnapshot<QuerySnapshot<Object?>> snapshot, int index) {
    final doc = snapshot.data!.docs[index];
    final int price = doc['price'] ?? 0;
    final int count = doc['count'] ?? 0;
    final int totalPrice = price * count;
    final String name = doc['name'] ?? 'Noma\'lum';

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Slidable(
        key: ValueKey(index),
        endActionPane: ActionPane(
          motion: const StretchMotion(),
          children: [
            SlidableAction(
              onPressed: (BuildContext context) {
                _showDeleteDialog(context, index, snapshot);
              },
              backgroundColor: cRed,
              foregroundColor: cWhite,
              icon: Icons.delete_outline,
              label: "O'chirish",
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            showBottomSheetForSale(
              id: doc.id,
              productId: doc.id,
              name: name,
              price: price,
              count: count,
              index: index,
            );
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cItemColor,
                  cItemColor.withOpacity(0.8),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: cButtonColor.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: cButtonColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: cButtonColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: cButtonColor.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.shopping_bag_outlined,
                        color: cButtonColor,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        name,
                        style: const TextStyle(
                          color: cWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoContainer(
                        icon: Icons.payments_outlined,
                        label: 'Narxi',
                        value: '${NumberFormat("#,###", "en_US").format(price)} UZS',
                        color: cButtonColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoContainer(
                        icon: Icons.inventory_2_outlined,
                        label: 'Miqdori',
                        value: '$count dona',
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: cButtonColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.calculate_outlined,
                            color: cButtonColor,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Jami Summa:',
                            style: TextStyle(
                              color: cGray,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${NumberFormat("#,###", "en_US").format(totalPrice)} UZS',
                        style: TextStyle(
                          color: cButtonColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoContainer({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  color: cGray,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, int index, AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            "Ogohlantirish",
            style: TextStyle(
              color: cWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            "Rostan ham ushbu mahsulotni o'chirmoqchimisiz?",
            style: TextStyle(color: cGray),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                "Yo'q",
                style: TextStyle(color: cGray),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: cRed,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () async {
                  bool result = await InternetConnectionChecker().hasConnection;
                  if (result == true) {
                    tradeHistory
                        .doc(widget.documentSnapshot.id)
                        .collection('actions')
                        .doc(snapshot.data?.docs[index].id)
                        .delete();
                    Navigator.pop(context);
                  } else {
                    showTopSnackBar(
                      Overlay.of(context),
                      CustomSnackBar.error(
                        message: "Ma'lumotlarni o'chirish uchun internet zarur",
                      ),
                    );
                    Navigator.pop(context);
                  }
                },
                child: const Text(
                  "Ha",
                  style: TextStyle(color: cWhite),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildTotalSection(int totalPrice) {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16,vertical: 4),
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cButtonColor,
            cButtonColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.3),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
        border: Border.all(
          color: cButtonColor.withOpacity(0.4),
          width: 1,
        ),
      ),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(4),
                decoration: BoxDecoration(
                  color: cBlack.withOpacity(0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.receipt_long_outlined,
                  color: cBlack,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              const Text(
                "Jami:",
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  color: cBlack,
                ),
              ),
            ],
          ),
          Text(
            "${NumberFormat("#,###", "en_US").format(totalPrice)} UZS",
            style: const TextStyle(
              fontSize: 16,
              fontWeight: FontWeight.bold,
              color: cBlack,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons(AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: StreamBuilder(
              stream: tradeHistory.doc(widget.documentSnapshot.id).snapshots(),
              builder: (BuildContext context, AsyncSnapshot<DocumentSnapshot> innerSnapshot) {
                if (innerSnapshot.connectionState == ConnectionState.active ||
                    innerSnapshot.connectionState == ConnectionState.done) {
                  bool isSold = innerSnapshot.hasData &&
                      innerSnapshot.data!.exists &&
                      innerSnapshot.data!.get('sold') == true;

                  return GestureDetector(
                    onTap: () async {
                      bool result = await InternetConnectionChecker().hasConnection;
                      if (isSold == false && result == true) {
                        for (int i = 0; i < snapshot.data!.docs.length; i++) {
                          fetchProductDetails(
                            productId: snapshot.data!.docs[i]['product_id'],
                            count: snapshot.data!.docs[i]['count'],
                          );
                        }
                        tradeHistory.doc(widget.documentSnapshot.id).update({"sold": true});
                        showTopSnackBar(
                          Overlay.of(context),
                          CustomSnackBar.success(
                            message: "Mahsulotlar ombordan muvoffaqiyatli chiqarildi!",
                          ),
                        );
                      } else {
                        showTopSnackBar(
                          Overlay.of(context),
                          CustomSnackBar.error(
                            message: "Savdo yopilgan!",
                          ),
                        );
                      }
                    },
                    child: Container(
                      height: 56,
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topLeft,
                          end: Alignment.bottomRight,
                          colors: isSold
                              ? [Colors.green, Colors.green.withOpacity(0.8)]
                              : [Colors.cyan, Colors.cyan.withOpacity(0.8)],
                        ),
                        borderRadius: BorderRadius.circular(16),
                        boxShadow: [
                          BoxShadow(
                            color: (isSold ? Colors.green : Colors.cyan).withOpacity(0.3),
                            blurRadius: 10,
                            offset: const Offset(0, 4),
                          ),
                        ],
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            isSold ? Icons.check_circle_outline : Icons.close_outlined,
                            color: cWhite,
                            size: 20,
                          ),
                          const SizedBox(width: 8),
                          Text(
                            isSold ? "Savdo yopilgan" : "Savdoni yopish",
                            style: const TextStyle(
                              color: cWhite,
                              fontSize: 14,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ],
                      ),
                    ),
                  );
                } else {
                  return Container(
                    height: 56,
                    decoration: BoxDecoration(
                      color: Colors.cyan.withOpacity(0.5),
                      borderRadius: BorderRadius.circular(16),
                    ),
                    child: const Center(
                      child: CircularProgressIndicator(
                        color: cWhite,
                        strokeWidth: 2,
                      ),
                    ),
                  );
                }
              },
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrintPage(
                      fullInfoProduct: buildList(snapshot, names.last),
                    ),
                  ),
                );
              },
              child: Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      cRed,
                      cRedDarkColor,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: cRed.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.print_outlined,
                      color: cWhite,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    const Text(
                      "Chek chiqarish",
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  names.last,
                  style: const TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Savdo tafsilotlari',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          StreamBuilder(
            stream: tradeHistory.doc(widget.documentSnapshot.id).snapshots(),
            builder: (BuildContext context, AsyncSnapshot<DocumentSnapshot> snapshot) {
              if (snapshot.connectionState == ConnectionState.active ||
                  snapshot.connectionState == ConnectionState.done) {
                bool isSold = snapshot.hasData &&
                    snapshot.data!.exists &&
                    snapshot.data!.get('sold') == true;

                // Only show the add button if the sale is not sold
                if (!isSold) {
                  return GestureDetector(
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (context) => HomePage(
                            saleId: widget.documentSnapshot.id,
                          ),
                        ),
                      );
                    },
                    child: Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: cButtonColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(
                          color: cButtonColor.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: const Icon(
                        Icons.add,
                        color: cButtonColor,
                        size: 20,
                      ),
                    ),
                  );
                } else {
                  // Return empty container when sale is sold
                  return const SizedBox.shrink();
                }
              } else {
                // Show loading state or empty container while loading
                return const SizedBox.shrink();
              }
            },
          ),
        ],
      ),
    );
  }

  showBottomSheetForSale({
    required String id,
    required String productId,
    required String name,
    required int price,
    required int count,
    required int index,
  }) {
    TextEditingController priceController = TextEditingController(
      text: NumberFormat("#,###", "en_US").format(price),
    );
    TextEditingController countController = TextEditingController(
      text: count.toString(),
    );

    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cBackgroundColor,
                  cBackgroundColor.withOpacity(0.9),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 20,
                  left: 24,
                  top: 20,
                  right: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 50,
                        height: 4,
                        decoration: BoxDecoration(
                          color: cGray.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),

                    // Header section
                    _buildEditHeader(name),
                    const SizedBox(height: 24),
                    // Price section
                    _buildEditPriceField(state, priceController),
                    const SizedBox(height: 20),
                    // Quantity section
                    _buildEditQuantitySection(state, countController),
                    const SizedBox(height: 32),

                    // Action buttons
                    _buildEditActionButtons(context, id, priceController, countController),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Widget _buildEditHeader(String name) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: cButtonColor.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.edit_outlined,
              color: cButtonColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mahsulotni tahrirlash',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 18,
                    color: cWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildEditPriceField(StateSetter state, TextEditingController priceController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.payments_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot narxi',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: cButtonColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: priceController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: cWhite),
            inputFormatters: [ThousandsFormatter()],
            decoration: InputDecoration(
              hintText: 'Narxni kiriting',
              hintStyle: TextStyle(color: cGray),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              suffixText: 'UZS',
              suffixStyle: TextStyle(
                color: cGray,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildEditQuantitySection(StateSetter state, TextEditingController countController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot miqdori',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: cButtonColor.withOpacity(0.3)),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  if (currentValue > 1) {
                    state(() {
                      countController.text = (currentValue - 1).toString();
                    });
                  }
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.remove,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: countController,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: cWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  decoration: InputDecoration(
                    hintText: '1',
                    hintStyle: TextStyle(color: cGray),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    suffixText: 'dona',
                    suffixStyle: TextStyle(
                      color: cGray,
                      fontSize: 12,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    if (value.isEmpty || int.tryParse(value) == null || int.parse(value) < 1) {
                      state(() {
                        countController.text = '';
                      });
                    }
                  },
                ),
              ),
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  state(() {
                    countController.text = (currentValue + 1).toString();
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.add,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEditActionButtons(BuildContext context, String id, TextEditingController priceController, TextEditingController countController) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: cGray.withOpacity(0.3)),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.close, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'Bekor qilish',
                      style: TextStyle(
                        color: Colors.red,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: GestureDetector(
            onTap: () async {
              bool result = await InternetConnectionChecker().hasConnection;
              if (result == true) {
                tradeHistory
                    .doc(widget.documentSnapshot.id)
                    .collection('actions')
                    .doc(id)
                    .update({
                  "count": int.parse(countController.text),
                  "price": int.parse(priceController.text.replaceAll(',', ''))
                });
                Navigator.pop(context);
              } else {
                showTopSnackBar(
                  Overlay.of(context),
                  CustomSnackBar.error(
                    message: "Ma'lumotlarni yangilash uchun internet zarur!",
                  ),
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cButtonColor,
                    cButtonColor.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check, color: cBlack, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'O\'zgartirish',
                      style: TextStyle(
                        color: cBlack,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}

class FullInfoProduct {
  final String name;
  final List<Product>? list;
  final int totalSum;

  FullInfoProduct(
      {required this.name, required this.list, required this.totalSum});
}
