import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/main.dart';
import 'package:market/print_page.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'klient_spinner.dart';
import 'model/product.dart';
import 'check_detail.dart';

class ShoppingListPage extends StatefulWidget {
  final Box productBox;
  final CollectionReference product;

  const ShoppingListPage(
      {super.key, required this.productBox, required this.product});

  @override
  State<ShoppingListPage> createState() => _ShoppingListPageState();
}

class _ShoppingListPageState extends State<ShoppingListPage> {
  TextEditingController priceController = TextEditingController();
  TextEditingController countController = TextEditingController();
  TextEditingController clientController = TextEditingController();
  bool hasText = false;
  String? clientName;

  final CollectionReference tradeTemporary =
      FirebaseFirestore.instance.collection("trade_temporary");
  final CollectionReference tradeHistory =
      FirebaseFirestore.instance.collection("trade_history");
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd HH:mm');
  final _formKey = GlobalKey<FormState>();

  final CollectionReference _product =
      FirebaseFirestore.instance.collection("products");

  void fetchProductDetails(
      {required String productId, required int count}) async {
    try {
      DocumentSnapshot<Map<String, dynamic>> snapshot = await _product
          .doc(productId)
          .get() as DocumentSnapshot<Map<String, dynamic>>;

      if (snapshot.exists) {
        int baseCount = snapshot.data()?["count"];
        int newCount = baseCount - count;

        // Ensure inventory never goes below zero
        if (newCount < 0) {
          newCount = 0;
          print('Warning: Attempted to reduce stock below zero for product $productId. Setting count to 0.');
        }

        widget.product.doc(productId).update({"count": newCount});
      } else {
        print('Document with ID $productId does not exist.');
      }
    } catch (e) {
      print('Error fetching product details: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      resizeToAvoidBottomInset:  false,
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16,vertical: 4),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Savatcha',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                ValueListenableBuilder(
                  valueListenable: productBox.listenable(),
                  builder: (context, value, child) {
                    return Text(
                      '${productBox.length} ta mahsulot',
                      style: TextStyle(
                        color: cGray,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                ),
              ],
            ),
          ),
          GestureDetector(
            onTap: () {
              _showClearDialog(context);
            },
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: cRed.withOpacity(0.2),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cRed.withOpacity(0.4),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.delete_outline,
                color: cRed,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showClearDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            "Ogohlantirish",
            style: TextStyle(
              color: cWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            "Rostan ham ushbu ro'yxatni tozalamoqchimisiz?",
            style: TextStyle(color: cGray),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                "Yo'q",
                style: TextStyle(color: cGray),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: cRed,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () {
                  productBox.clear();
                  Navigator.pop(context);
                },
                child: const Text(
                  "Ha",
                  style: TextStyle(color: cWhite),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildBody(BuildContext context) {
    return GestureDetector(
      onTap: () {
        // Hide keyboard and unfocus any text fields when tapping outside
        FocusScope.of(context).unfocus();
      },
      child: Column(
        children: [
          UserSpinner(
            onUserSelected: (String? userId, String userName) {
              clientName = userName;
            },
          ),
          Expanded(
            child: ValueListenableBuilder(
              valueListenable: productBox.listenable(),
              builder: (context, value, child) {
                if (productBox.isEmpty) {
                  return _buildEmptyState();
                }

                return ListView.builder(
                  padding: const EdgeInsets.all(16),
                  itemCount: productBox.length,
                  itemBuilder: (BuildContext context, int index) {
                    return _buildModernProductCard(context, index);
                  },
                );
              },
            ),
          ),
          _buildGrandTotal(),
          _buildActionButtons(),
        ],
      ),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.shopping_cart_outlined,
              size: 64,
              color: cGray,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Savatcha bo\'sh',
            style: TextStyle(
              color: cWhite,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Mahsulotlar qo\'shish uchun asosiy sahifaga o\'ting',
            style: TextStyle(
              color: cGray,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernProductCard(BuildContext context, int index) {
    final product = productBox.getAt(index);
    if (product == null) return const SizedBox.shrink();

    final String name = product.name ?? "Noma'lum";
    final int price = product.price ?? 0;
    final int count = product.count ?? 0;
    final int totalPrice = price * count;

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Slidable(
        key: ValueKey(index),
        endActionPane: ActionPane(
          motion: const StretchMotion(),
          children: [
            SlidableAction(
              onPressed: (BuildContext context) {
                _showDeleteDialog(context, index);
              },
              backgroundColor: cRed,
              foregroundColor: cWhite,
              icon: Icons.delete_outline,
              label: "O'chirish",
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            showBottomSheetForSale(
              product.id ?? "-1",
              product.id ?? "-1",
              name,
              price,
              count,
              index,
            );
          },
          child: Container(
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(16),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cItemColor,
                  cItemColor.withOpacity(0.8),
                ],
              ),
              boxShadow: [
                BoxShadow(
                  color: cButtonColor.withOpacity(0.1),
                  blurRadius: 10,
                  offset: const Offset(0, 4),
                ),
              ],
              border: Border.all(
                color: cButtonColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: cButtonColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(10),
                        border: Border.all(
                          color: cButtonColor.withOpacity(0.4),
                          width: 1,
                        ),
                      ),
                      child: Icon(
                        Icons.shopping_bag_outlined,
                        color: cButtonColor,
                        size: 18,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Text(
                        name,
                        style: const TextStyle(
                          color: cWhite,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 12),
                Row(
                  children: [
                    Expanded(
                      child: _buildInfoContainer(
                        icon: Icons.payments_outlined,
                        label: 'Narxi',
                        value: '${NumberFormat("#,###", "en_US").format(price)} UZS',
                        color: cButtonColor,
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: _buildInfoContainer(
                        icon: Icons.inventory_2_outlined,
                        label: 'Miqdori',
                        value: '$count dona',
                        color: Colors.green,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: cButtonColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.calculate_outlined,
                            color: cButtonColor,
                            size: 16,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Jami Summa:',
                            style: TextStyle(
                              color: cGray,
                              fontSize: 12,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                        ],
                      ),
                      Text(
                        '${NumberFormat("#,###", "en_US").format(totalPrice)} UZS',
                        style: TextStyle(
                          color: cButtonColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildInfoContainer({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(10),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 14,
              ),
              const SizedBox(width: 4),
              Text(
                label,
                style: TextStyle(
                  color: cGray,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, int index) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          title: const Text(
            "Ogohlantirish",
            style: TextStyle(
              color: cWhite,
              fontWeight: FontWeight.bold,
            ),
          ),
          content: const Text(
            "Rostan ham ushbu mahsulotni o'chirmoqchimisiz?",
            style: TextStyle(color: cGray),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: const Text(
                "Yo'q",
                style: TextStyle(color: cGray),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                color: cRed,
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () {
                  productBox.deleteAt(index);
                  Navigator.pop(context);
                },
                child: const Text(
                  "Ha",
                  style: TextStyle(color: cWhite),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildGrandTotal() {
    return ValueListenableBuilder(
      valueListenable: productBox.listenable(),
      builder: (context, value, child) {
        int grandTotal = 0;
        List<Product> products = productBox.values.toList().cast<Product>();

        for (var product in products) {
          if (product.price != null && product.count != null) {
            grandTotal += product.price * product.count;
          }
        }

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cButtonColor,
                cButtonColor.withOpacity(0.8),
              ],
            ),
            borderRadius: BorderRadius.circular(16),
            boxShadow: [
              BoxShadow(
                color: cButtonColor.withOpacity(0.3),
                blurRadius: 15,
                offset: const Offset(0, 5),
              ),
            ],
            border: Border.all(
              color: cButtonColor.withOpacity(0.4),
              width: 1,
            ),
          ),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Row(
                children: [
                  Container(
                    decoration: BoxDecoration(
                      color: cBlack.withOpacity(0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(
                      Icons.receipt_long_outlined,
                      color: cBlack,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 12),
                  const Text(
                    "Jami:",
                    style: TextStyle(
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                      color: cBlack,
                    ),
                  ),
                ],
              ),
              Text(
                "${NumberFormat("#,###", "en_US").format(grandTotal)} UZS",
                style: const TextStyle(
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                  color: cBlack,
                ),
              ),
            ],
          ),
        );
      },
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: () {
                // Check if there are products in the cart
                if (productBox.isEmpty) {
                  showTopSnackBar(
                    Overlay.of(context),
                    CustomSnackBar.error(
                      message: "Savatcha bo'sh! Chek chiqarish uchun mahsulot qo'shing.",
                    ),
                  );
                  return;
                }

                // Prepare data for printing
                List<Product> productList = productBox.values.toList().cast<Product>();
                int totalSum = 0;

                // Calculate total sum
                for (var product in productList) {
                  totalSum += product.price * product.count;
                }

                // Create FullInfoProduct object
                FullInfoProduct fullInfoProduct = FullInfoProduct(
                  name: clientName ?? " ",
                  list: productList,
                  totalSum: totalSum,
                );

                // Navigate to PrintPage
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => PrintPage(
                      fullInfoProduct: fullInfoProduct,
                    ),
                  ),
                );
              },
              child: Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      cRed,
                      cRedDarkColor,
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: cRed.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.print_outlined,
                      color: cWhite,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      "Chek chiqarish",
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 14,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: GestureDetector(
              onTap: () async {
                bool result = await InternetConnectionChecker().hasConnection;
                if (result == true) {
                  try {
                    WriteBatch batch = FirebaseFirestore.instance.batch();
                    List<Product> list = productBox.values.toList().cast<Product>() ?? [];
                    print(list);
                    int summa = 0;
                    list.forEach((element) {
                      summa = summa + element.price * element.count;
                    });

                    tradeHistory.add({
                      "name": setSearchParam(clientName ?? "Empty"),
                      "date": formatterDate.format(DateTime.now()),
                      "totalPrice": summa,
                      "sold": false
                    }).then((value) async {
                      CollectionReference actionsRef = tradeHistory
                          .doc(value.id)
                          .collection('actions');
                      for (var product in list) {
                        DocumentReference docRef = actionsRef.doc(product.id);
                        batch.set(docRef, product.toJson());
                      }
                      await batch.commit(); // Execute all writes in one go
                    });
                    productBox.clear();
                    clientController.clear();
                    showTopSnackBar(
                      Overlay.of(context),
                      CustomSnackBar.success(
                        message: "Ma'lumotlar savdo bo'limiga saqlandi!",
                      ),
                    );
                    Navigator.pop(context);
                  } catch (e) {
                    print(e);
                    showTopSnackBar(
                      Overlay.of(context),
                      CustomSnackBar.error(
                        message: e.toString(),
                      ),
                    );
                  }
                } else {
                  showTopSnackBar(
                    Overlay.of(context),
                    CustomSnackBar.error(
                      message: "Sotuv bo'limiga saqlash uchun internet zarur!",
                    ),
                  );
                }
              },
              child: Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      cButtonColor,
                      cButtonColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: cButtonColor.withOpacity(0.3),
                      blurRadius: 10,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      Icons.save_outlined,
                      color: cBlack,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Flexible(
                      child: Text(
                        "Savdo bo'limiga saqlash",
                        style: TextStyle(
                          color: cBlack,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  showBottomSheetForSale(String id, String productId, String name, int price,
      int count, int index) {
    priceController = TextEditingController(
        text: NumberFormat("#,###", "en_US").format(price));
    countController = TextEditingController(text: count.toString());
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cBackgroundColor,
                  cBackgroundColor.withOpacity(0.9),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 20,
                  left: 24,
                  top: 20,
                  right: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 50,
                        height: 4,
                        decoration: BoxDecoration(
                          color: cGray.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Header section
                    _buildModernHeader(name),
                    const SizedBox(height: 24),
                    // Price section
                    _buildModernPriceField(state),
                    const SizedBox(height: 20),
                    // Quantity section
                    _buildModernQuantitySection(state),
                    const SizedBox(height: 32),

                    // Action buttons
                    _buildModernActionButtons(context, index, name, id),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Widget _buildModernHeader(String name) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: cButtonColor.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.edit_outlined,
              color: cButtonColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Mahsulotni tahrirlash',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 18,
                    color: cWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildModernPriceField(StateSetter state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.payments_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot narxi',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: cButtonColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: priceController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: cWhite),
            inputFormatters: [ThousandsFormatter()],
            decoration: InputDecoration(
              hintText: 'Narxni kiriting',
              hintStyle: TextStyle(color: cGray),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              suffixText: 'UZS',
              suffixStyle: TextStyle(
                color: cGray,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernQuantitySection(StateSetter state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot miqdori',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: cButtonColor.withOpacity(0.3)),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  if (currentValue > 1) {
                    state(() {
                      countController.text = (currentValue - 1).toString();
                    });
                  }
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.remove,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: countController,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: cWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  decoration: InputDecoration(
                    hintText: '1',
                    hintStyle: TextStyle(color: cGray),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    suffixText: 'dona',
                    suffixStyle: TextStyle(
                      color: cGray,
                      fontSize: 12,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    if (value.isEmpty || int.tryParse(value) == null || int.parse(value) < 1) {
                      state(() {
                        countController.text = '';
                      });
                    }
                  },
                ),
              ),
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  state(() {
                    countController.text = (currentValue + 1).toString();
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.add,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildModernActionButtons(BuildContext context, int index, String name, String id) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: cGray.withOpacity(0.3)),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.close, color: Colors.red, size: 20),
                    const SizedBox(width: 8),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: GestureDetector(
            onTap: () {
              try {
                productBox.putAt(
                  index,
                  Product(
                    name: name,
                    price: int.parse(priceController.text.replaceAll(',', '')),
                    count: int.parse(countController.text),
                    id: id,
                    productId: id,
                  ),
                );
                Navigator.pop(context);
              } catch (e) {
                // Handle error if needed
                Navigator.pop(context);
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cButtonColor,
                    cButtonColor.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.check, color: cBlack, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      'O\'zgartirish',
                      style: TextStyle(
                        color: cBlack,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Future<List<Product>> fetchProducts() async {
    try {
      QuerySnapshot querySnapshot = await FirebaseFirestore.instance
          .collection('trade_temporary')
          .doc('temporary')
          .collection('actions')
          .get();
      print(querySnapshot.docs);
      var lis = querySnapshot.docs.map((doc) {
        return Product(
          id: doc.id,
          name: doc['name'] ?? 'Unknown',
          price: (doc['price'] ?? 0) as int,
          count: (doc['count'] ?? 0) as int,
          productId: doc['product_id'],
        );
      }).toList();
      print(lis);
      return lis;

      // Refresh UI after fetching data
    } catch (e) {
      print("Error fetching products: $e");
      throw Exception(e);
    }
  }

  Future<void> deleteTemporaryWithSubcollections() async {
    try {
      // Reference to the document
      final DocumentReference tempDocRef = tradeTemporary.doc('temporary');

      // Since we know we have an 'actions' subcollection, we'll directly reference it
      final CollectionReference actionsRef = tempDocRef.collection('actions');

      // Get all documents in the actions subcollection
      final QuerySnapshot snapshot = await actionsRef.get();

      // If there are documents to delete
      if (snapshot.docs.isNotEmpty) {
        // Create a batch for deletion
        WriteBatch batch = FirebaseFirestore.instance.batch();

        // Add delete operations for each document
        for (var doc in snapshot.docs) {
          batch.delete(doc.reference);
        }

        // Commit the batch
        await batch.commit();
      }

      // Finally delete the document itself
      await tempDocRef.delete();

      print('Document and all subcollections deleted successfully');
    } catch (e) {
      print('Error during deletion: $e');
    }
  }
}

class TradeHistory {
  List<Map<String, dynamic>> tradeHistory;
  String name;
  String date;

  TradeHistory(
      {required this.tradeHistory, required this.name, required this.date});

  Map<String, dynamic> toJson() {
    return {
      'tradeHistory': tradeHistory,
      'name': name,
      'date': date,
    };
  }
}

setSearchParam(String caseNumber) {
  List<String> caseSearchList = [];
  String temp = "";
  for (int i = 0; i < caseNumber.length; i++) {
    temp = temp + caseNumber[i];
    caseSearchList.add(temp);
  }
  return caseSearchList;
}
