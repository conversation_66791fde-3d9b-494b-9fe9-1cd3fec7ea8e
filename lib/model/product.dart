import 'package:hive/hive.dart';
part 'product.g.dart';
List<Map<String, dynamic>> toJsonList(List<Product> products) {
  return products.map((product) => product.toJson()).toList();
}

@HiveType(typeId: 0)
class Product {
  @HiveField(0)
  String id;
  @HiveField(1)
  String productId;
  @HiveField(2)
  String name;
  @HiveField(3)
  int price;
  @HiveField(4)
  int count;

  Product(
      {required this.id,
      required this.productId,
      required this.name,
      required this.price,
      required this.count});

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'product_id': productId,
      'name': name,
      'price': price,
      'count': count,
    };
  }

  @override
  String toString() {
    return 'Product{id: $id,productId:$productId name: $name, price: $price, count: $count}';
  }
}
