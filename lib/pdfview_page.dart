// import 'dart:typed_data';
//
// import 'package:flutter/material.dart';
// import 'package:hive_flutter/hive_flutter.dart';
// import 'package:intl/intl.dart';
// import 'package:market/print_page.dart';
// import 'package:printing/printing.dart';
// import 'package:pdf/pdf.dart';
// import 'package:pdf/widgets.dart' as pw;
//
// import 'model/product.dart';
//
// class PdfViewPage extends StatefulWidget {
//   final String client;
//
//   const PdfViewPage({super.key, required this.client});
//
//   @override
//   State<PdfViewPage> createState() => _PdfViewPageState();
// }
//
// class _PdfViewPageState extends State<PdfViewPage> {
//   Box<Product> productBox = Hive.box("product");
//   List<Product> productList = [];
//   final DateFormat formatterDate = DateFormat('yyyy-MM-dd,HH:mm');
//   int summa = 0;
//
//   @override
//   void initState() {
//     super.initState();
//     getData();
//     productList.forEach((element) {
//       summa = summa + element.price * element.count;
//     });
//   }
//
//   getData() async {
//     productList = productBox.values.toList().cast<Product>() ?? [];
//   }
//
//   @override
//   Widget build(BuildContext context) {
//     return Scaffold(
//       appBar: AppBar(),
//       body: Stack(
//         children: [
//           PdfPreview(
//             build: (format) {
//               return _generatePdf(
//                   PdfPageFormat.roll80, "Check", productList, widget.client);
//             },
//             onPrinted: (value) {
//               print("Printed:${value}");
//             },
//           ),
//           Positioned(
//               bottom: 80,
//               right: 20,
//               child: FloatingActionButton(
//                 onPressed: () {
//                   Navigator.push(
//                       context,
//                       MaterialPageRoute(
//                           builder: (_) => PrintPage(client: widget.client)));
//                 },
//                 child: Icon(Icons.print),
//               ))
//         ],
//       ),
//     );
//   }
//
//   Future<Uint8List> _generatePdf(PdfPageFormat format, String title,
//       List<Product> list, String client) async {
//     final pdf = pw.Document(version: PdfVersion.pdf_1_5, compress: true);
//     final font = await PdfGoogleFonts.nunitoExtraLight();
//
//     pdf.addPage(
//       pw.Page(
//         pageFormat: format,
//         build: (context) {
//           return pw.Column(children: [
//             pw.Row(children: [
//               pw.Text(client, style: pw.TextStyle(fontSize: 12))
//             ]),
//             // pw.SizedBox(height: 15),
//             pw.Row(
//                 mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//                 children: [
//                   pw.Text("Sana va vaqt:", style: pw.TextStyle(fontSize: 12)),
//                   pw.Text(formatterDate.format(DateTime.now()),
//                       style: pw.TextStyle(fontSize: 12))
//                 ]),
//             pw.SizedBox(height: 10),
//             pw.ListView.builder(
//               itemCount: list.length,
//               itemBuilder: (context, index) {
//                 return pw.Container(
//                     child: pw.Column(
//                         crossAxisAlignment: pw.CrossAxisAlignment.start,
//                         children: [
//                       pw.SizedBox(height: 4),
//                       pw.Text(list[index].name,
//                           style: pw.TextStyle(
//                             fontSize: 12,
//                           )),
//                       pw.SizedBox(
//                         height: 4,
//                       ),
//                       pw.Row(
//                           mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//                           children: [
//                             pw.Text(
//                                 "${list[index].count} x ${NumberFormat("#,###", "en_US").format(list[index].price).toString()}",
//                                 style: pw.TextStyle(fontSize: 12)),
//                             pw.Text(
//                                 "${NumberFormat("#,###", "en_US").format(list[index].count * list[index].price).toString()}",
//                                 style: pw.TextStyle(fontSize: 12))
//                           ]),
//                       pw.SizedBox(height: 4),
//                       pw.Container(height: 2, color: PdfColor(0, 0, 0))
//                     ]));
//               },
//             ),
//             pw.SizedBox(height: 20),
//             pw.Row(
//                 mainAxisAlignment: pw.MainAxisAlignment.spaceBetween,
//                 children: [
//                   pw.Text("Jami umumiy summa:",
//                       style: pw.TextStyle(fontSize: 12)),
//                   pw.Text(
//                       NumberFormat("#,###", "en_US").format(summa).toString(),
//                       style: pw.TextStyle(fontSize: 12))
//                 ]),
//           ]);
//         },
//       ),
//     );
//     return pdf.save();
//   }
// }
