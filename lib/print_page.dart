import 'dart:async';
import 'dart:developer';
import 'dart:io';

import 'package:esc_pos_utils_plus/esc_pos_utils_plus.dart';
import 'package:flutter/material.dart';
import 'package:flutter_pos_printer_platform_image_3/flutter_pos_printer_platform_image_3.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import 'check_detail.dart';
import 'core/function.dart';
import 'core/util/app_constants.dart';
import 'model/product.dart';

class PrintPage extends StatefulWidget {
  final FullInfoProduct fullInfoProduct;

  const PrintPage({Key? key, required this.fullInfoProduct}) : super(key: key);

  @override
  State<PrintPage> createState() => _PrintPageState();
}

class _PrintPageState extends State<PrintPage> {
  // Printer Type [bluetooth, usb, network]
  var defaultPrinterType = PrinterType.network;
  var _isBle = false;
  var _reconnect = false;
  var _isConnected = false;
  var printerManager = PrinterManager.instance;
  var devices = <BluetoothPrinter>[];
  StreamSubscription<PrinterDevice>? _subscription;
  StreamSubscription<BTStatus>? _subscriptionBtStatus;
  StreamSubscription<USBStatus>? _subscriptionUsbStatus;
  BTStatus _currentStatus = BTStatus.none;

  // _currentUsbStatus is only supports on Android
  // ignore: unused_field
  USBStatus _currentUsbStatus = USBStatus.none;
  List<int>? pendingTask;
  String _ipAddress = '';
  String _port = '9100';
  final _ipController = TextEditingController();
  final _portController = TextEditingController();
  BluetoothPrinter? selectedPrinter;
  List<Product> productList = [];
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd,HH:mm');
  int summa = 0;

  @override
  void initState() {
    // Keep network as default for all platforms
    // if (Platform.isWindows) defaultPrinterType = PrinterType.usb;
    super.initState();
    _portController.text = _port;
    _loadSavedPrinter();
    _scan();

    // subscription to listen change status of bluetooth connection
    _subscriptionBtStatus =
        PrinterManager.instance.stateBluetooth.listen((status) {
      log(' ----------------- status bt $status ------------------ ');
      _currentStatus = status;
      if (status == BTStatus.connected) {
        setState(() {
          _isConnected = true;
        });
        // Save settings when Bluetooth connects successfully
        _savePrinterSettings();
      }
      if (status == BTStatus.none) {
        setState(() {
          _isConnected = false;
        });
      }
      if (status == BTStatus.connected && pendingTask != null) {
        if (Platform.isAndroid) {
          Future.delayed(const Duration(milliseconds: 1000), () {
            PrinterManager.instance
                .send(type: PrinterType.bluetooth, bytes: pendingTask!);
            pendingTask = null;
          });
        } else if (Platform.isIOS) {
          PrinterManager.instance
              .send(type: PrinterType.bluetooth, bytes: pendingTask!);
          pendingTask = null;
        }
      }
    });
    //  PrinterManager.instance.stateUSB is only supports on Android
    _subscriptionUsbStatus = PrinterManager.instance.stateUSB.listen((status) {
      log(' ----------------- status usb $status ------------------ ');
      _currentUsbStatus = status;
      if (Platform.isAndroid) {
        if (status == USBStatus.connected && pendingTask != null) {
          Future.delayed(const Duration(milliseconds: 1000), () {
            PrinterManager.instance
                .send(type: PrinterType.usb, bytes: pendingTask!);
            pendingTask = null;
          });
        }
      }
    });
  }

  @override
  void dispose() {
    _subscription?.cancel();
    _subscriptionBtStatus?.cancel();
    _subscriptionUsbStatus?.cancel();
    _portController.dispose();
    _ipController.dispose();
    super.dispose();
  }

  // method to scan devices according PrinterType
  void _scan() {
    devices.clear();
    _subscription = printerManager
        .discovery(type: defaultPrinterType, isBle: _isBle)
        .listen((device) {
      devices.add(BluetoothPrinter(
        deviceName: device.name,
        address: device.address,
        isBle: _isBle,
        vendorId: device.vendorId,
        productId: device.productId,
        typePrinter: defaultPrinterType,
      ));
      setState(() {});
    });
  }

  void setPort(String value) {
    if (value.isEmpty) value = '9100';
    _port = value;
    var device = BluetoothPrinter(
      deviceName: value,
      address: _ipAddress,
      port: _port,
      typePrinter: PrinterType.network,
      state: false,
    );
    selectDevice(device);
  }

  void setIpAddress(String value) {
    _ipAddress = value;
    var device = BluetoothPrinter(
      deviceName: value,
      address: _ipAddress,
      port: _port,
      typePrinter: PrinterType.network,
      state: false,
    );
    selectDevice(device);
  }

  void selectDevice(BluetoothPrinter device) async {
    if (selectedPrinter != null) {
      if ((device.address != selectedPrinter!.address) ||
          (device.typePrinter == PrinterType.usb &&
              selectedPrinter!.vendorId != device.vendorId)) {
        await PrinterManager.instance
            .disconnect(type: selectedPrinter!.typePrinter);
      }
    }

    selectedPrinter = device;

    // Save the selected printer automatically
    await _savePrinterSettings();

    setState(() {});

    log('Printer selected and saved: ${device.deviceName}');
  }

  Future _printReceiveTest() async {
    // Check if printer is connected before attempting to print
    if (!_isConnected) {
      log('Cannot print: Printer is not connected (Ulanmagan)');
      // Show error message to user
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Printer ulanmagan! Avval printerni ulang.',
            style: TextStyle(color: cWhite),
          ),
          backgroundColor: Colors.red,
          duration: Duration(seconds: 3),
        ),
      );
      return;
    }

    log('Starting print process - Printer is connected (Ulangan)');

    List<int> bytes = [];
    // Xprinter XP-N160I
    final profile = await CapabilityProfile.load(name: 'XP-N160I');

    // PaperSize.mm80 or PaperSize.mm58
    final generator = Generator(
      PaperSize.mm80,
      profile,
    );
    // bytes += generator.setGlobalCodeTable('PC1251');

    bytes += generator.rawBytes([0x1B, 0x74, 17]); // ESC t 17

// Convert and print Cyrillic text
    bytes +=
        generator.textEncoded(stringToUint8List(widget.fullInfoProduct.name),
            styles: const PosStyles(
              align: PosAlign.left,
              fontType: PosFontType.fontA,
              height: PosTextSize.size2,
              width: PosTextSize.size2
            ));
    bytes += generator.emptyLines(2);

    bytes += generator.row(
      [
        PosColumn(
          textEncoded: stringToUint8List("Вакт ва соат"),
          width: 7,
          styles: const PosStyles(
            align: PosAlign.left,
            fontType: PosFontType.fontA,
          ),
        ),
        PosColumn(
            text: formatterDate.format(DateTime.now()),
            width: 5,
            styles: const PosStyles(
              align: PosAlign.right,
              fontType: PosFontType.fontA,
            )),
      ],
    );
    bytes += generator.emptyLines(1);
    widget.fullInfoProduct.list?.forEach((element) {
      bytes += generator.textEncoded(stringToUint8List(element.name),
          styles: PosStyles(
            align: PosAlign.left,
            fontType: PosFontType.fontA,
          ));
      bytes += generator.row([
        PosColumn(
            text:
                "${element.count}x${NumberFormat("#,###", "en_US").format((element.price))}",
            width: 8,
            styles: const PosStyles(
              align: PosAlign.left,
              fontType: PosFontType.fontA,
            )),
        PosColumn(
            text: NumberFormat("#,###", "en_US")
                .format((element.price * element.count))
                .toString(),
            width: 4,
            styles: const PosStyles(
              align: PosAlign.right,
              fontType: PosFontType.fontA,
            )),
      ]);
      bytes +=
          generator.text('------------------------------------------------');
    });
    bytes += generator.row([
      PosColumn(
          textEncoded: stringToUint8List("Жами умумий сумма:"),
          width: 8,
          styles: const PosStyles(
            align: PosAlign.left,
            fontType: PosFontType.fontA,
          )),
      PosColumn(
          text: NumberFormat("#,###", "en_US")
              .format(widget.fullInfoProduct.totalSum)
              .toString(),
          width: 4,
          styles: const PosStyles(
            align: PosAlign.right,
            fontType: PosFontType.fontA,
          )),
    ]);

    _printEscPos(bytes, generator);
  }

  /// print ticket
  void _printEscPos(List<int> bytes, Generator generator) async {
    if (selectedPrinter == null) return;
    var bluetoothPrinter = selectedPrinter!;

    // Prepare print data based on printer type
    switch (bluetoothPrinter.typePrinter) {
      case PrinterType.usb:
        bytes += generator.feed(2);
        bytes += generator.cut();
        pendingTask = null;
        break;
      case PrinterType.bluetooth:
        bytes += generator.cut();
        pendingTask = null;
        if (Platform.isAndroid) pendingTask = bytes;
        break;
      case PrinterType.network:
        bytes += generator.feed(2);
        bytes += generator.cut();
        break;
      default:
    }
    // Send print data only if connected
    try {
      if (bluetoothPrinter.typePrinter == PrinterType.bluetooth &&
          Platform.isAndroid) {
        // For Bluetooth on Android, check both connection status
        if (_currentStatus == BTStatus.connected && _isConnected) {
          await printerManager.send(type: bluetoothPrinter.typePrinter, bytes: bytes);
          pendingTask = null;
          log('Print sent successfully via Bluetooth');
          _showPrintSuccessMessage();
        } else {
          log('Bluetooth not properly connected, cannot print');
          _showPrintErrorMessage('Bluetooth printer ulanmagan!');
          return;
        }
      } else {
        // For USB and Network, check connection status
        if (_isConnected) {
          await printerManager.send(type: bluetoothPrinter.typePrinter, bytes: bytes);
          log('Print sent successfully via ${bluetoothPrinter.typePrinter}');
          _showPrintSuccessMessage();
        } else {
          log('Printer not connected, cannot print');
          _showPrintErrorMessage('Printer ulanmagan!');
          return;
        }
      }
    } catch (e) {
      log('Error sending print data: $e');
      _showPrintErrorMessage('Chop etishda xatolik: $e');
    }
    //productBox.clear();
  }

  // Save printer settings to GetStorage
  Future<void> _savePrinterSettings() async {
    if (selectedPrinter == null) return;

    final storage = GetStorage();
    final printerData = {
      'deviceName': selectedPrinter!.deviceName,
      'address': selectedPrinter!.address,
      'port': selectedPrinter!.port,
      'typePrinter': selectedPrinter!.typePrinter.index,
      'vendorId': selectedPrinter!.vendorId,
      'productId': selectedPrinter!.productId,
      'isBle': selectedPrinter!.isBle,
      'defaultPrinterType': defaultPrinterType.index,
      'ipAddress': _ipAddress,
      'portNumber': _port,
      'isBleEnabled': _isBle,
      'reconnectEnabled': _reconnect,
    };

    await storage.write('saved_printer', printerData);
    log('Printer settings saved: ${selectedPrinter!.deviceName}');
  }

  // Load printer settings from GetStorage
  Future<void> _loadSavedPrinter() async {
    try {
      final storage = GetStorage();
      final printerData = storage.read('saved_printer');

      if (printerData != null) {
        // Restore printer type
        defaultPrinterType = PrinterType.values[printerData['defaultPrinterType'] ?? 2];

        // Restore settings
        _ipAddress = printerData['ipAddress'] ?? '';
        _port = printerData['portNumber'] ?? '9100';
        _isBle = printerData['isBleEnabled'] ?? false;
        _reconnect = printerData['reconnectEnabled'] ?? false;

        // Update controllers
        _ipController.text = _ipAddress;
        _portController.text = _port;

        // Recreate the saved printer
        selectedPrinter = BluetoothPrinter(
          deviceName: printerData['deviceName'],
          address: printerData['address'],
          port: printerData['portNumber'],
          typePrinter: PrinterType.values[printerData['typePrinter'] ?? 2],
          vendorId: printerData['vendorId'],
          productId: printerData['productId'],
          isBle: printerData['isBle'] ?? false,
          state: false,
        );

        log('Loaded saved printer: ${selectedPrinter!.deviceName}');

        // Auto-connect to saved printer
        Future.delayed(const Duration(milliseconds: 1500), () {
          _autoConnectToSavedPrinter();
        });

        setState(() {});
      }
    } catch (e) {
      log('Error loading saved printer: $e');
    }
  }

  // Auto-connect to saved printer
  Future<void> _autoConnectToSavedPrinter() async {
    if (selectedPrinter == null) return;

    log('Auto-connecting to saved printer...');
    try {
      await _connectDevice();
      if (_isConnected) {
        _showAutoConnectSuccessMessage();
      }
    } catch (e) {
      log('Auto-connect failed: $e');
      _showAutoConnectFailedMessage();
    }
  }

  // Clear saved printer settings
  Future<void> _clearSavedPrinter() async {
    final storage = GetStorage();
    await storage.remove('saved_printer');
    log('Saved printer settings cleared');
  }

  void _showAutoConnectSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.wifi, color: cWhite, size: 20),
            const SizedBox(width: 8),
            Text(
              'Saqlangan printer avtomatik ulandi!',
              style: TextStyle(color: cWhite),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 3),
      ),
    );
  }

  void _showAutoConnectFailedMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.wifi_off, color: cWhite, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                'Saqlangan printerga ulanib bo\'lmadi. Qaytadan ulanishga harakat qiling.',
                style: TextStyle(color: cWhite),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.orange,
        duration: Duration(seconds: 4),
      ),
    );
  }

  void _showForgetPrinterDialog() {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.delete_outline,
                  color: Colors.orange,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "Printerni Unutish",
                style: TextStyle(
                  color: cWhite,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          content: Text(
            "Saqlangan printer sozlamalarini o'chirmoqchimisiz?\n\nKeyingi safar qaytadan tanlash va ulash kerak bo'ladi.",
            style: TextStyle(color: cGray, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Bekor qilish",
                style: TextStyle(color: cGray, fontSize: 14),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.orange,
                    Colors.orange.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () async {
                  await _clearSavedPrinter();
                  if (selectedPrinter != null && _isConnected) {
                    await printerManager.disconnect(type: selectedPrinter!.typePrinter);
                  }
                  setState(() {
                    selectedPrinter = null;
                    _isConnected = false;
                  });
                  Navigator.pop(context);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        'Printer sozlamalari o\'chirildi',
                        style: TextStyle(color: cWhite),
                      ),
                      backgroundColor: Colors.orange,
                      duration: Duration(seconds: 2),
                    ),
                  );
                },
                child: Text(
                  "Ha, Unutish",
                  style: TextStyle(color: cWhite, fontSize: 14),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  void _showPrintSuccessMessage() {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.check_circle, color: cWhite, size: 20),
            const SizedBox(width: 8),
            Text(
              'Chek muvaffaqiyatli chop etildi!',
              style: TextStyle(color: cWhite),
            ),
          ],
        ),
        backgroundColor: Colors.green,
        duration: Duration(seconds: 2),
      ),
    );
  }

  void _showPrintErrorMessage(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(Icons.error_outline, color: cWhite, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                message,
                style: TextStyle(color: cWhite),
              ),
            ),
          ],
        ),
        backgroundColor: Colors.red,
        duration: Duration(seconds: 3),
      ),
    );
  }

  // conectar dispositivo
  _connectDevice() async {
    try {
      if (selectedPrinter == null) return;

      log('Attempting to connect to ${selectedPrinter!.deviceName}');

      switch (selectedPrinter!.typePrinter) {
        case PrinterType.usb:
          await printerManager.connect(
              type: selectedPrinter!.typePrinter,
              model: UsbPrinterInput(
                  name: selectedPrinter!.deviceName,
                  productId: selectedPrinter!.productId,
                  vendorId: selectedPrinter!.vendorId));
          setState(() {
            _isConnected = true;
          });
          await _savePrinterSettings();
          log('USB printer connected successfully');
          break;
        case PrinterType.bluetooth:
          await printerManager.connect(
              type: selectedPrinter!.typePrinter,
              model: BluetoothPrinterInput(
                  name: selectedPrinter!.deviceName,
                  address: selectedPrinter!.address!,
                  isBle: selectedPrinter!.isBle ?? false,
                  autoConnect: _reconnect));
          // For Bluetooth, connection status is handled by the listener
          log('Bluetooth connection initiated');
          break;
        case PrinterType.network:
          await printerManager.connect(
              type: selectedPrinter!.typePrinter,
              model: TcpPrinterInput(ipAddress: selectedPrinter!.address!));
          setState(() {
            _isConnected = true;
          });
          await _savePrinterSettings();
          log('Network printer connected successfully');
          break;
        default:
      }
    } catch (e) {
      log('Connection error: $e');
      setState(() {
        _isConnected = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildProfessionalHeader(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 16),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: _buildPrintingInterface(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cItemColor.withOpacity(0.8),
                    cItemColor.withOpacity(0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Chek Chop Etish',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Printer boshqaruv paneli',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrintingInterface() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildConnectionSection(),
          const SizedBox(height: 24),
          _buildPrinterTypeSection(),
          const SizedBox(height: 24),
          _buildPrinterOptionsSection(),
          const SizedBox(height: 24),
          _buildDeviceListSection(),
          const SizedBox(height: 24),
          _buildNetworkSection(),
        ],
      ),
    );
  }

  Widget _buildConnectionSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: _isConnected
                      ? Colors.green.withOpacity(0.2)
                      : Colors.orange.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  _isConnected ? Icons.link : Icons.link_off,
                  color: _isConnected ? Colors.green : Colors.orange,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Printer Ulanishi',
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Row(
                      children: [
                        Text(
                          _isConnected ? 'Ulangan' : 'Ulanmagan',
                          style: TextStyle(
                            color: _isConnected ? Colors.green : Colors.orange,
                            fontSize: 14,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                        if (selectedPrinter != null) ...[
                          const SizedBox(width: 8),
                          Container(
                            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: Colors.blue.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.blue.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Icon(
                                  Icons.bookmark,
                                  color: Colors.blue,
                                  size: 10,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Saqlangan',
                                  style: TextStyle(
                                    color: Colors.blue,
                                    fontSize: 10,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ],
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          Row(
            children: [
              Expanded(
                child: _buildActionButton(
                  text: 'Ulash',
                  icon: Icons.link,
                  color: Colors.green,
                  isEnabled: selectedPrinter != null && !_isConnected,
                  onPressed: () => _connectDevice(),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  text: 'Uzish',
                  icon: Icons.link_off,
                  color: Colors.red,
                  isEnabled: selectedPrinter != null && _isConnected,
                  onPressed: () {
                    if (selectedPrinter != null) {
                      printerManager.disconnect(type: selectedPrinter!.typePrinter);
                    }
                    setState(() {
                      _isConnected = false;
                    });
                  },
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: _buildActionButton(
                  text: 'Unutish',
                  icon: Icons.delete_outline,
                  color: Colors.orange,
                  isEnabled: selectedPrinter != null,
                  onPressed: () => _showForgetPrinterDialog(),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildActionButton({
    required String text,
    required IconData icon,
    required Color color,
    required bool isEnabled,
    required VoidCallback onPressed,
  }) {
    return GestureDetector(
      onTap: isEnabled ? onPressed : null,
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          gradient: isEnabled
              ? LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    color,
                    color.withOpacity(0.8),
                  ],
                )
              : LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cGray.withOpacity(0.3),
                    cGray.withOpacity(0.2),
                  ],
                ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: isEnabled
              ? [
                  BoxShadow(
                    color: color.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ]
              : [],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isEnabled ? cWhite : cGray,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              text,
              style: TextStyle(
                color: isEnabled ? cWhite : cGray,
                fontSize: 14,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPrinterTypeSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cButtonColor.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.print,
                  color: cButtonColor,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Printer Turi',
                style: TextStyle(
                  color: cWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          Container(
            decoration: BoxDecoration(
              color: cBackgroundColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: cButtonColor.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: DropdownButtonFormField<PrinterType>(
              value: defaultPrinterType,
              dropdownColor: cItemColor,
              style: TextStyle(color: cWhite),
              decoration: InputDecoration(
                prefixIcon: Icon(
                  Icons.print,
                  color: cButtonColor,
                  size: 20,
                ),
                labelText: "Printer turini tanlang",
                labelStyle: TextStyle(color: cGray, fontSize: 14),
                border: InputBorder.none,
                contentPadding: EdgeInsets.all(16),
              ),
              items: <DropdownMenuItem<PrinterType>>[
                if (Platform.isAndroid || Platform.isIOS)
                  DropdownMenuItem(
                    value: PrinterType.bluetooth,
                    child: Text("Bluetooth", style: TextStyle(color: cWhite)),
                  ),
                if (Platform.isAndroid || Platform.isWindows)
                  DropdownMenuItem(
                    value: PrinterType.usb,
                    child: Text("USB", style: TextStyle(color: cWhite)),
                  ),
                DropdownMenuItem(
                  value: PrinterType.network,
                  child: Text("Wi-Fi Tarmoq", style: TextStyle(color: cWhite)),
                ),
              ],
              onChanged: (PrinterType? value) {
                if (value != null) {
                  setState(() {
                    defaultPrinterType = value;
                    selectedPrinter = null;
                    _isBle = false;
                    _isConnected = false;
                    _scan();
                  });
                }
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPrinterOptionsSection() {
    if (defaultPrinterType != PrinterType.bluetooth || !Platform.isAndroid) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.bluetooth,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Bluetooth Sozlamalari',
                style: TextStyle(
                  color: cWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildModernSwitch(
            title: 'Kam Energiya Rejimi (BLE)',
            subtitle: 'Bluetooth Low Energy qo\'llab-quvvatlash',
            value: _isBle,
            onChanged: (value) {
              setState(() {
                _isBle = value;
                _isConnected = false;
                selectedPrinter = null;
                _scan();
              });
            },
          ),
          const SizedBox(height: 16),
          _buildModernSwitch(
            title: 'Avtomatik Qayta Ulanish',
            subtitle: 'Ulanish uzilganda avtomatik qayta ulanish',
            value: _reconnect,
            onChanged: (value) {
              setState(() {
                _reconnect = value;
              });
            },
          ),
        ],
      ),
    );
  }

  Widget _buildModernSwitch({
    required String title,
    required String subtitle,
    required bool value,
    required ValueChanged<bool> onChanged,
  }) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: cBackgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: value ? Colors.green.withOpacity(0.3) : cGray.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  subtitle,
                  style: TextStyle(
                    color: cGray,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          const SizedBox(width: 16),
          GestureDetector(
            onTap: () => onChanged(!value),
            child: Container(
              width: 50,
              height: 28,
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(14),
                color: value ? Colors.green : cGray.withOpacity(0.3),
              ),
              child: AnimatedAlign(
                duration: const Duration(milliseconds: 200),
                alignment: value ? Alignment.centerRight : Alignment.centerLeft,
                child: Container(
                  width: 24,
                  height: 24,
                  margin: const EdgeInsets.all(2),
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(12),
                    color: cWhite,
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.2),
                        blurRadius: 4,
                        offset: const Offset(0, 2),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeviceListSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.purple.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.devices,
                  color: Colors.purple,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Text(
                  'Mavjud Printerlar',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              GestureDetector(
                onTap: _scan,
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    Icons.refresh,
                    color: cButtonColor,
                    size: 18,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          if (devices.isEmpty)
            _buildEmptyDeviceState()
          else
            ...devices.map((device) => _buildDeviceCard(device)).toList(),
        ],
      ),
    );
  }

  Widget _buildEmptyDeviceState() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: cBackgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: cGray.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Center(
        child: Column(
          children: [
            Icon(
              Icons.search_off,
              color: cGray,
              size: 48,
            ),
            const SizedBox(height: 12),
            Text(
              'Hech qanday printer topilmadi',
              style: TextStyle(
                color: cWhite,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Printerni yoqing va qayta qidiring',
              style: TextStyle(
                color: cGray,
                fontSize: 14,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeviceCard(BluetoothPrinter device) {
    final bool isSelected = selectedPrinter != null &&
        ((device.typePrinter == PrinterType.usb && Platform.isWindows
                ? device.deviceName == selectedPrinter!.deviceName
                : device.vendorId != null &&
                    selectedPrinter!.vendorId == device.vendorId) ||
            (device.address != null &&
                selectedPrinter!.address == device.address));

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isSelected
            ? Colors.green.withOpacity(0.1)
            : cBackgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: isSelected
              ? Colors.green.withOpacity(0.5)
              : cGray.withOpacity(0.3),
          width: 1.5,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: isSelected
                      ? Colors.green.withOpacity(0.2)
                      : cGray.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  isSelected ? Icons.check_circle : Icons.print,
                  color: isSelected ? Colors.green : cGray,
                  size: 18,
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      device.deviceName ?? 'Noma\'lum Printer',
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (device.address != null &&
                        !(Platform.isAndroid &&
                            defaultPrinterType == PrinterType.usb) &&
                        !Platform.isWindows)
                      Text(
                        device.address!,
                        style: TextStyle(
                          color: cGray,
                          fontSize: 12,
                        ),
                      ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Row(
            children: [
              Expanded(
                child: GestureDetector(
                  onTap: () => selectDevice(device),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: isSelected
                            ? [Colors.green, Colors.green.withOpacity(0.8)]
                            : [cButtonColor, cButtonColor.withOpacity(0.8)],
                      ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        isSelected ? 'Tanlangan' : 'Tanlash',
                        style: TextStyle(
                          color: cWhite,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: GestureDetector(
                  onTap: (selectedPrinter == null ||
                          device.deviceName != selectedPrinter?.deviceName ||
                          !_isConnected)
                      ? null
                      : () => _printReceiveTest(),
                  child: Container(
                    padding: const EdgeInsets.symmetric(vertical: 10),
                    decoration: BoxDecoration(
                      gradient: (selectedPrinter != null &&
                              device.deviceName == selectedPrinter?.deviceName &&
                              _isConnected)
                          ? LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.orange,
                                Colors.orange.withOpacity(0.8)
                              ],
                            )
                          : LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                cGray.withOpacity(0.3),
                                cGray.withOpacity(0.2)
                              ],
                            ),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Center(
                      child: Text(
                        _isConnected ? 'Chop Etish' : 'Ulanmagan',
                        style: TextStyle(
                          color: (selectedPrinter != null &&
                                  device.deviceName == selectedPrinter?.deviceName &&
                                  _isConnected)
                              ? cWhite
                              : cGray,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkSection() {
    if (defaultPrinterType != PrinterType.network) {
      return const SizedBox.shrink();
    }

    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 15,
            offset: const Offset(0, 5),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.wifi,
                  color: Colors.blue,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Tarmoq Sozlamalari',
                style: TextStyle(
                  color: cWhite,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),
          _buildNetworkField(
            controller: _ipController,
            label: 'IP Manzil',
            hint: '*************',
            icon: Icons.wifi,
            onChanged: setIpAddress,
          ),
          const SizedBox(height: 16),
          _buildNetworkField(
            controller: _portController,
            label: 'Port',
            hint: '9100',
            icon: Icons.numbers_outlined,
            onChanged: setPort,
          ),
          const SizedBox(height: 20),
          GestureDetector(
            onTap: _isConnected ? () async {
              if (_ipController.text.isNotEmpty) {
                setIpAddress(_ipController.text);
              }
              _printReceiveTest();
            } : null,
            child: Container(
              width: double.infinity,
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: _isConnected
                      ? [
                          Colors.orange,
                          Colors.orange.withOpacity(0.8),
                        ]
                      : [
                          cGray.withOpacity(0.3),
                          cGray.withOpacity(0.2),
                        ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: _isConnected
                    ? [
                        BoxShadow(
                          color: Colors.orange.withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : [],
              ),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    _isConnected ? Icons.print : Icons.link_off,
                    color: _isConnected ? cWhite : cGray,
                    size: 20,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    _isConnected ? 'Test Chek Chop Etish' : 'Avval Ulang',
                    style: TextStyle(
                      color: _isConnected ? cWhite : cGray,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildNetworkField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    required ValueChanged<String> onChanged,
  }) {
    return Container(
      decoration: BoxDecoration(
        color: cBackgroundColor.withOpacity(0.3),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: TextFormField(
        controller: controller,
        style: TextStyle(color: cWhite),
        keyboardType: TextInputType.numberWithOptions(signed: true),
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          labelStyle: TextStyle(color: cGray, fontSize: 14),
          hintStyle: TextStyle(color: cGray.withOpacity(0.7), fontSize: 14),
          prefixIcon: Icon(icon, color: cButtonColor, size: 20),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
        onChanged: onChanged,
      ),
    );
  }
}

class BluetoothPrinter {
  int? id;
  String? deviceName;
  String? address;
  String? port;
  String? vendorId;
  String? productId;
  bool? isBle;

  PrinterType typePrinter;
  bool? state;

  BluetoothPrinter(
      {this.deviceName,
      this.address,
      this.port,
      this.state,
      this.vendorId,
      this.productId,
      this.typePrinter = PrinterType.bluetooth,
      this.isBle = false});
}
