import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

class UserSpinner extends StatefulWidget {
  final void Function(String? userId, String userName) onUserSelected;
  final String? initialUserId;
  final String? initialUserName;

  const UserSpinner({
    Key? key,
    required this.onUserSelected,
    this.initialUserId,
    this.initialUserName,
  }) : super(key: key);

  @override
  State<UserSpinner> createState() => _UserSpinnerState();
}

class _UserSpinnerState extends State<UserSpinner> {
  final TextEditingController _textController = TextEditingController();
  final CollectionReference _usersCollection =
      FirebaseFirestore.instance.collection('users');
  List<DocumentSnapshot> _allUsers = [];
  List<DocumentSnapshot> _filteredUsers = [];
  bool _showSuggestions = false;
  final FocusNode _focusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    if (widget.initialUserName != null) {
      _textController.text = widget.initialUserName!;
    }
    _loadUsers();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _textController.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChange() {
    if (_focusNode.hasFocus) {
      setState(() {
        _showSuggestions = true;
        _filterUsers(_textController.text);
      });
    } else {
      // Hide suggestions when focus is lost
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  Future<void> _loadUsers() async {
    try {
      final snapshot = await _usersCollection.orderBy('name').get();
      setState(() {
        _allUsers = snapshot.docs;
        _filteredUsers = _allUsers;
      });
    } catch (e) {
      print('Error loading users: $e');
    }
  }

  void _filterUsers(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredUsers = _allUsers;
      } else {
        _filteredUsers = _allUsers.where((doc) {
          final data = doc.data() as Map<String, dynamic>;
          final userName = data['name'] as String;
          return userName.toLowerCase().contains(query.toLowerCase());
        }).toList();
      }
    });
  }

  void _selectUser(String? userId, String userName) {
    setState(() {
      _textController.text = userName;
      _showSuggestions = false;
    });
    _focusNode.unfocus();
    widget.onUserSelected(userId, userName);
  }

  void _onTextChanged(String value) {
    _filterUsers(value);
    if (value.isNotEmpty) {
      setState(() {
        _showSuggestions = true;
      });
      // If user is typing a custom name, call the callback with null userId
      widget.onUserSelected(null, value);
    } else {
      setState(() {
        _showSuggestions = false;
      });
    }
  }

  void _hideSuggestions() {
    setState(() {
      _showSuggestions = false;
    });
  }

  // Add a new user to the database (similar to UserManagementPage)
  Future<void> _addNewUser(String userName) async {
    if (userName.trim().isEmpty) return;
    final String nameToAdd = userName.trim();

    try {
      // Check if a user with the same name already exists
      QuerySnapshot existingUsers =
          await _usersCollection.where('name', isEqualTo: nameToAdd).get();

      if (existingUsers.docs.isNotEmpty) {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            message: "Bu ismdagi foydalanuvchi allaqachon mavjud!",
          ),
        );
        return;
      }

      // Add new user
      DocumentReference docRef = await _usersCollection.add({
        'name': nameToAdd,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.success(
          message: 'Klient qo\'shildi',
        ),
      );

      // Select the newly added user
      _selectUser(docRef.id, nameToAdd);

      // Reload users to update the suggestions
      _loadUsers();
    } catch (e) {
      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.error(message: 'Xatolik: ${e.toString()}'),
      );
    }
  }

  @override
  Widget build(BuildContext context) {
    return GestureDetector(
      behavior: HitTestBehavior.opaque,
      onTap: () {
        // Only hide suggestions if tapping outside the text field
        if (!_focusNode.hasFocus) {
          _hideSuggestions();
        }
      },
      child: Card(
        color: cBackgroundColor,
        elevation: 2,
        child: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 16.0, vertical: 8.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const Text(
                'Klient tanlang yoki yozing',
                style: TextStyle(
                  fontWeight: FontWeight.bold,
                  fontSize: 16,
                  color: cWhite,
                ),
              ),
              const SizedBox(height: 8),
              Column(
                children: [
                  // Text field for typing/searching
                  GestureDetector(
                    onTap: () {
                      // Prevent the parent GestureDetector from hiding suggestions
                      // when tapping on the text field
                    },
                    child: Container(
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey),
                        color: cItemColor,
                      ),
                      child: TextField(
                        controller: _textController,
                        focusNode: _focusNode,
                        style: const TextStyle(color: cWhite),
                        decoration: const InputDecoration(
                          hintText: 'Klient ismini yozing yoki qidiring...',
                          hintStyle: TextStyle(color: Colors.grey),
                          border: InputBorder.none,
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 16.0,
                            vertical: 12.0,
                          ),
                          suffixIcon: Icon(
                            Icons.search,
                            color: Colors.grey,
                          ),
                        ),
                        onChanged: _onTextChanged,
                        onTap: () {
                          setState(() {
                            _showSuggestions = true;
                            _filterUsers(_textController.text);
                          });
                        },
                      ),
                    ),
                  ),
                  // Suggestions list
                  if (_showSuggestions && _filteredUsers.isNotEmpty)
                    Container(
                      margin: const EdgeInsets.only(top: 4),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(8),
                        border: Border.all(color: Colors.grey),
                        color: cItemColor,
                      ),
                      constraints: const BoxConstraints(maxHeight: 200),
                      child: ListView.builder(
                        shrinkWrap: true,
                        itemCount: _filteredUsers.length,
                        itemBuilder: (context, index) {
                          final doc = _filteredUsers[index];
                          final data = doc.data() as Map<String, dynamic>;
                          final userName = data['name'] as String;
                          final userId = doc.id;

                          return ListTile(
                            dense: true,
                            title: Text(
                              userName,
                              style: const TextStyle(color: cWhite),
                            ),
                            leading: const Icon(
                              Icons.person,
                              color: Colors.grey,
                              size: 20,
                            ),
                            onTap: () => _selectUser(userId, userName),
                          );
                        },
                      ),
                    ),
                  // Show message when no suggestions found but user is typing
                  if (_showSuggestions &&
                      _filteredUsers.isEmpty &&
                      _textController.text.isNotEmpty)
                    GestureDetector(
                      onTap: () => _addNewUser(_textController.text),
                      child: Container(
                        margin: const EdgeInsets.only(top: 4),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(8),
                          border: Border.all(color: cButtonColor),
                          color: cItemColor,
                        ),
                        child: Row(
                          children: [
                            const Icon(
                              Icons.add_circle_outline,
                              color: cButtonColor,
                              size: 20,
                            ),
                            const SizedBox(width: 8),
                            Expanded(
                              child: Text(
                                'Yangi klient qo\'shish: "${_textController.text}"',
                                style: const TextStyle(
                                  color: cButtonColor,
                                  fontWeight: FontWeight.w500,
                                ),
                              ),
                            ),
                            const Icon(
                              Icons.arrow_forward_ios,
                              color: cButtonColor,
                              size: 16,
                            ),
                          ],
                        ),
                      ),
                    ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }
}

// Example usage:
class ExamplePage extends StatelessWidget {
  const ExamplePage({Key? key}) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('User Selector')),
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            UserSpinner(
              onUserSelected: (userId, userName) {
                // Do something with the selected user
                print('Selected user: $userName (ID: $userId)');

                // Example: Show a snackbar with the selected user
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(content: Text('Selected: $userName')),
                );
              },
            ),
            const SizedBox(height: 24),
            const Text('Rest of your form or content goes here...'),
          ],
        ),
      ),
    );
  }
}
