import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

class UserManagementPage extends StatefulWidget {
  const UserManagementPage({Key? key}) : super(key: key);

  @override
  State<UserManagementPage> createState() => _UserManagementPageState();
}

class _UserManagementPageState extends State<UserManagementPage> {
  final TextEditingController _nameController = TextEditingController();
  final CollectionReference _usersCollection =
      FirebaseFirestore.instance.collection('users');
  String? _editingUserId;

  @override
  void dispose() {
    _nameController.dispose();
    super.dispose();
  }

  // Add a new user
  Future<void> _addUser() async {
    if (_nameController.text.isEmpty) return;
    final String nameToAdd = _nameController.text.trim();

    try {
      // Check if a user with the same name already exists
      QuerySnapshot existingUsers =
          await _usersCollection.where('name', isEqualTo: nameToAdd).get();

      // If we're updating a user, we should exclude the current user from the check
      bool nameExists = false;
      if (_editingUserId != null) {
        // Only count as duplicate if another user (not the one being edited) has this name
        nameExists = existingUsers.docs.any((doc) => doc.id != _editingUserId);
      } else {
        // For new users, any match means the name exists
        nameExists = existingUsers.docs.isNotEmpty;
      }

      if (nameExists) {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            message: "Bu ismdagi foydalanuvchi allaqachon mavjud!",
          ),
        );
        return; // Exit the function without adding/updating
      }

      // Proceed with update or add since the name is unique
      if (_editingUserId != null) {
        // Update existing user
        await _usersCollection.doc(_editingUserId).update({
          'name': nameToAdd,
          'updatedAt': FieldValue.serverTimestamp(),
        });
        _editingUserId = null;
      } else {
        // Add new user
        await _usersCollection.add({
          'name': nameToAdd,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
      }

      // Clear the text field after operation
      _nameController.clear();


      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.success(
          message: _editingUserId != null
              ? 'Klient ismi yangilandi!'
              : 'Klient qo\'shildi',
        ),
      );
      // Show success message
    } catch (e) {
      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.error(
          message: 'Xatolik: ${e.toString()}'
        ),
      );
    }
  }

  // Delete a user
  Future<void> _deleteUser(String userId) async {
    bool hasInternet = await InternetConnectionChecker().hasConnection;
    if (hasInternet) {
      try {
        await _usersCollection.doc(userId).delete();
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.success(
              message: 'Klient muvaffaqiyatli o\'chirildi!'
          ),
        );
      } catch (e) {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
              message: 'Klientni o\'chirishda xatolik: ${e.toString()}'
          ),
        );
      }
    } else {
      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.error(
            message: 'Internet aloqasi yo\'q! Qaytadan urinib ko\'ring.'
        ),
      );
    }
  }

  // Set up editing mode
  void _editUser(String userId, String name) {
    setState(() {
      _editingUserId = userId;
      _nameController.text = name;
    });
  }

  // Cancel editing mode
  void _cancelEdit() {
    setState(() {
      _editingUserId = null;
      _nameController.clear();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Klient boshqaruvi',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Klientlarni qo\'shish va boshqarish',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return Column(
      children: [
        _buildModernInputSection(),
        const SizedBox(height: 16),
        Expanded(child: _buildUsersList()),
        _buildActionButtons(),
      ],
    );
  }

  Widget _buildModernInputSection() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.person_add_outlined,
                color: cButtonColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                _editingUserId != null ? 'Klient ma\'lumotlarini yangilash' : 'Yangi klient qo\'shish',
                style: TextStyle(
                  color: cWhite,
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildModernTextField(),
        ],
      ),
    );
  }

  Widget _buildModernTextField() {
    return Container(
      decoration: BoxDecoration(
        color: cBackgroundColor.withOpacity(0.6),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: TextField(
        controller: _nameController,
        style: TextStyle(color: cWhite),
        decoration: InputDecoration(
          hintText: 'Klient ismini kiriting',
          hintStyle: TextStyle(color: cGray),
          prefixIcon: Icon(
            Icons.person_outline,
            color: cButtonColor,
          ),
          border: InputBorder.none,
          contentPadding: EdgeInsets.all(16),
        ),
      ),
    );
  }

  Widget _buildUsersList() {
    return StreamBuilder<QuerySnapshot>(
      stream: _usersCollection
          .orderBy('createdAt', descending: true)
          .snapshots(),
      builder: (context, snapshot) {
        if (snapshot.hasError) {
          return Center(
            child: Text(
              'Xatolik: ${snapshot.error}',
              style: TextStyle(color: cWhite),
            ),
          );
        }

        if (snapshot.connectionState == ConnectionState.waiting) {
          return Center(
            child: CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(cButtonColor),
            ),
          );
        }

        final docs = snapshot.data!.docs;

        if (docs.isEmpty) {
          return _buildEmptyState();
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(horizontal: 16),
          itemCount: docs.length,
          itemBuilder: (context, index) {
            final doc = docs[index];
            final userData = doc.data() as Map<String, dynamic>;
            final userId = doc.id;
            final userName = userData['name'] as String;

            return _buildModernUserCard(userId, userName, index);
          },
        );
      },
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.3),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              Icons.people_outline,
              size: 64,
              color: cGray,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Klientlar topilmadi',
            style: TextStyle(
              color: cWhite,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Yangi klient qo\'shish uchun yuqoridagi formadan foydalaning',
            style: TextStyle(
              color: cGray,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildModernUserCard(String userId, String userName, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        boxShadow: [
          BoxShadow(
            color: cButtonColor.withOpacity(0.1),
            blurRadius: 10,
            offset: const Offset(0, 4),
          ),
        ],
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: ListTile(
        contentPadding: const EdgeInsets.all(16),
        leading: Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: cButtonColor.withOpacity(0.2),
            borderRadius: BorderRadius.circular(10),
            border: Border.all(
              color: cButtonColor.withOpacity(0.4),
              width: 1,
            ),
          ),
          child: Icon(
            Icons.person_outline,
            color: cButtonColor,
            size: 20,
          ),
        ),
        title: Text(
          userName,
          style: TextStyle(
            color: cWhite,
            fontSize: 16,
            fontWeight: FontWeight.bold,
          ),
        ),
        subtitle: Text(
          'Klient #${index + 1}',
          style: TextStyle(
            color: cGray,
            fontSize: 12,
          ),
        ),
        trailing: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            GestureDetector(
              onTap: () => _editUser(userId, userName),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.blue.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.4),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.edit_outlined,
                  color: Colors.blue,
                  size: 16,
                ),
              ),
            ),
            const SizedBox(width: 8),
            GestureDetector(
              onTap: () => _showDeleteDialog(context, userId, userName),
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cRed.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                  border: Border.all(
                    color: cRed.withOpacity(0.4),
                    width: 1,
                  ),
                ),
                child: Icon(
                  Icons.delete_outline,
                  color: cRed,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showDeleteDialog(BuildContext context, String userId, String userName) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: cItemColor,
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: const Text(
          'O\'chirishni tasdiqlang',
          style: TextStyle(
            color: cWhite,
            fontWeight: FontWeight.bold,
          ),
        ),
        content: Text(
          'Rostan ham "$userName" klientni o\'chirmoqchimisiz?',
          style: TextStyle(color: cGray),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Bekor qilish',
              style: TextStyle(color: cGray),
            ),
          ),
          Container(
            decoration: BoxDecoration(
              color: cRed,
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextButton(
              onPressed: () {
                Navigator.pop(context);
                _deleteUser(userId);
              },
              child: const Text(
                'O\'chirish',
                style: TextStyle(color: cWhite),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          Expanded(
            child: GestureDetector(
              onTap: _addUser,
              child: Container(
                height: 56,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      cButtonColor,
                      cButtonColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(16),
                  boxShadow: [
                    BoxShadow(
                      color: cButtonColor.withOpacity(0.3),
                      blurRadius: 15,
                      offset: const Offset(0, 5),
                    ),
                  ],
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(
                      _editingUserId != null ? Icons.update_outlined : Icons.person_add_outlined,
                      color: cBlack,
                      size: 20,
                    ),
                    const SizedBox(width: 8),
                    Text(
                      _editingUserId != null ? 'Yangilash' : 'Klient qo\'shish',
                      style: TextStyle(
                        color: cBlack,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
          if (_editingUserId != null) ...[
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: _cancelEdit,
                child: Container(
                  height: 56,
                  decoration: BoxDecoration(
                    color: cGray.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: cGray.withOpacity(0.4),
                      width: 1,
                    ),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(
                        Icons.cancel_outlined,
                        color: cGray,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Bekor qilish',
                        style: TextStyle(
                          color: cGray,
                          fontSize: 16,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }
}
