import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_pagination/firebase_pagination.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../../core/util/app_constants.dart';

class HistoryPage extends StatefulWidget {
  final String id;
  final int balance;

  const HistoryPage({super.key, required this.id, required this.balance});

  @override
  State<HistoryPage> createState() => _HistoryPageState();
}

class _HistoryPageState extends State<HistoryPage> {
  final getStorage = GetStorage();

  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
        app: Firebase.app(),
      );

  // var formattedDate = DateFormat('dd.MM.yyyy HH:mm');
  final DateFormat formattedDate = DateFormat('yyyy-MM-dd HH:mm');

  final formatterWithDecimals = NumberFormat("#,###");

  void _showDeleteConfirmationDialog({
    required BuildContext context,
    required String historyId,
    required bool add,
    required int number,
  }) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.red.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.warning_outlined,
                  color: Colors.red,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                "Ogohlantirish",
                style: TextStyle(
                  color: cWhite,
                  fontWeight: FontWeight.bold,
                  fontSize: 18,
                ),
              ),
            ],
          ),
          content: Text(
            "Rostan ham ushbu qarz tarixini o'chirmoqchimisiz?\n\nBu amal qaytarib bo'lmaydi.",
            style: TextStyle(color: cGray, fontSize: 14),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Text(
                "Yo'q",
                style: TextStyle(color: cGray, fontSize: 14),
              ),
            ),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    Colors.red,
                    Colors.red.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: TextButton(
                onPressed: () {
                  int balance = widget.balance;
                  secondDb
                      .collection('calculation_list')
                      .doc(widget.id)
                      .collection('history')
                      .doc(historyId)
                      .delete()
                      .then((onValue) {
                    balance = balance - number;
                    secondDb
                        .collection("calculation_list")
                        .doc(widget.id)
                        .update({"balance": balance});
                  });
                  Navigator.pop(context);
                },
                child: Text(
                  "Ha, O'chirish",
                  style: TextStyle(color: cWhite, fontSize: 14),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildProfessionalHeader(context),
              _buildBalanceCard(),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 16),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(32),
                      topRight: Radius.circular(32),
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.1),
                        blurRadius: 20,
                        offset: const Offset(0, -5),
                      ),
                    ],
                  ),
                  child: _buildHistoryList(),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cItemColor.withOpacity(0.8),
                    cItemColor.withOpacity(0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Qarz Tarixi',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Barcha operatsiyalar ro\'yxati',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBalanceCard() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Colors.red,
            Colors.red.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(20),
        boxShadow: [
          BoxShadow(
            color: Colors.red.withOpacity(0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: cWhite.withOpacity(0.2),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: cWhite.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Icon(
              Icons.account_balance_wallet_outlined,
              color: cWhite,
              size: 28,
            ),
          ),
          const SizedBox(width: 20),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Umumiy Qarz Miqdori',
                  style: TextStyle(
                    color: cWhite.withOpacity(0.9),
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  '${formatterWithDecimals.format(widget.balance.abs())} UZS',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                    letterSpacing: 0.5,
                  ),
                ),
                const SizedBox(height: 4),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
                  decoration: BoxDecoration(
                    color: cWhite.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    widget.balance == 0 ? 'To\'langan' : 'Faol Qarz',
                    style: TextStyle(
                      color: cWhite,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildHistoryList() {
    return FirestorePagination(
      query: secondDb
          .collection('calculation_list')
          .doc(widget.id)
          .collection("history")
          .orderBy("date", descending: true),
      itemBuilder: (context, documentSnapshot, index) {
        return _buildProfessionalHistoryCard(documentSnapshot, index);
      },
    );
  }

  Widget _buildProfessionalHistoryCard(DocumentSnapshot documentSnapshot, int index) {
    final bool isPayment = documentSnapshot['add'] == true;
    final int amount = documentSnapshot['balance'] ?? 0;
    final String title = documentSnapshot['title'] ?? 'Noma\'lum';
    final String comment = documentSnapshot['comment'] ?? '';
    final DateTime date = documentSnapshot['date'].toDate();

    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20, vertical: 8),
      child: Slidable(
        key: ValueKey(index),
        endActionPane: ActionPane(
          motion: const StretchMotion(),
          children: [
            SlidableAction(
              onPressed: (BuildContext context) {
                _showDeleteConfirmationDialog(
                  context: context,
                  historyId: documentSnapshot.id,
                  add: documentSnapshot['add'],
                  number: documentSnapshot['balance'],
                );
              },
              backgroundColor: Colors.red,
              foregroundColor: cWhite,
              icon: Icons.delete_outline,
              label: "O'chirish",
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(20),
                bottomLeft: Radius.circular(20),
              ),
            ),
          ],
        ),
        child: Container(
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(20),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                cItemColor,
                cItemColor.withOpacity(0.8),
              ],
            ),
            boxShadow: [
              BoxShadow(
                color: (isPayment ? Colors.green : Colors.red).withOpacity(0.15),
                blurRadius: 15,
                offset: const Offset(0, 6),
              ),
            ],
            border: Border.all(
              color: (isPayment ? Colors.green : Colors.red).withOpacity(0.3),
              width: 1.5,
            ),
          ),
          padding: const EdgeInsets.all(20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        begin: Alignment.topLeft,
                        end: Alignment.bottomRight,
                        colors: [
                          (isPayment ? Colors.green : Colors.red),
                          (isPayment ? Colors.green : Colors.red).withOpacity(0.8),
                        ],
                      ),
                      borderRadius: BorderRadius.circular(12),
                      boxShadow: [
                        BoxShadow(
                          color: (isPayment ? Colors.green : Colors.red).withOpacity(0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ],
                    ),
                    child: Icon(
                      isPayment
                          ? Icons.trending_down_outlined
                          : Icons.trending_up_outlined,
                      color: cWhite,
                      size: 20,
                    ),
                  ),
                  const SizedBox(width: 16),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          title,
                          style: const TextStyle(
                            color: cWhite,
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        if (comment.isNotEmpty)
                          Text(
                            comment,
                            style: TextStyle(
                              color: cGray,
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                      ],
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
                    decoration: BoxDecoration(
                      color: (isPayment ? Colors.green : Colors.red).withOpacity(0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(
                        color: (isPayment ? Colors.green : Colors.red).withOpacity(0.4),
                        width: 1,
                      ),
                    ),
                    child: Text(
                      isPayment ? 'To\'lov' : 'Qarz',
                      style: TextStyle(
                        color: isPayment ? Colors.green : Colors.red,
                        fontSize: 11,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),
              const SizedBox(height: 16),
              Row(
                children: [
                  Expanded(
                    child: _buildInfoContainer(
                      icon: Icons.calendar_today_outlined,
                      label: 'Sana',
                      value: formattedDate.format(date),
                      color: cButtonColor,
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: _buildInfoContainer(
                      icon: Icons.payments_outlined,
                      label: 'Summa',
                      value: '${formatterWithDecimals.format(amount.abs())} UZS',
                      color: isPayment ? Colors.green : Colors.red,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInfoContainer({
    required IconData icon,
    required String label,
    required String value,
    required Color color,
  }) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: color.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                icon,
                color: color,
                size: 14,
              ),
              const SizedBox(width: 6),
              Text(
                label,
                style: TextStyle(
                  color: cGray,
                  fontSize: 11,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
          const SizedBox(height: 6),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 12,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
