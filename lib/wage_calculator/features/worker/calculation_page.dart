import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../core/util/app_constants.dart';
import '../../calculator.dart';

class CalculationPage extends StatefulWidget {
  final int? balance;
  final String? title;
  final String? id;

  const CalculationPage({super.key, this.balance, this.title, this.id});

  @override
  State<CalculationPage> createState() => _CalculationPageState();
}

class _CalculationPageState extends State<CalculationPage> {
  late DateTime dateTime;
  var formattedDate = DateFormat('dd,MM, yyyy');
  final getStorage = GetStorage();

  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
        app: Firebase.app(),
      );
  final _formKey = GlobalKey<FormState>();
  int temporaryValue = 0;
  final formatterWithDecimals = NumberFormat("#,###");
  int minus = 0;
  int add = 0;

  TextEditingController ticketName = TextEditingController();
  TextEditingController number = TextEditingController();
  TextEditingController comment = TextEditingController();
  final TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    dateTime = DateTime.now();
    if (widget.id != null && widget.balance != null) {
      temporaryValue = widget.balance!;
      minus = temporaryValue;
      add = temporaryValue;
      ticketName = TextEditingController(text: widget.title);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildProfessionalHeader(),
              Expanded(
                child: SingleChildScrollView(
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        _buildUnifiedFormCard(),
                        const SizedBox(height: 32),
                        widget.id == null ? _buildNewDebtActions() : _buildExistingDebtActions(),
                      ],
                    ),
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildProfessionalHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cItemColor.withOpacity(0.8),
                    cItemColor.withOpacity(0.6),
                  ],
                ),
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1.5,
                ),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.id == null
                      ? 'Yangi Qarz Yozuvi'
                      : 'Qarz Tahrirlash',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  widget.id == null
                      ? 'Yangi mijoz qo\'shish'
                      : widget.title ?? 'Mavjud mijoz',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUnifiedFormCard() {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.9),
          ],
        ),
        borderRadius: BorderRadius.circular(24),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1.5,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.2),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      Colors.red.withOpacity(0.3),
                      Colors.red.withOpacity(0.2),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  Icons.receipt_long,
                  color: Colors.red,
                  size: 24,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      widget.id == null ? 'Yangi Qarz Yozuvi' : 'Qarz Tahrirlash',
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 20,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Barcha ma\'lumotlarni to\'ldiring',
                      style: TextStyle(
                        color: cGray,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 32),

          // Client Name Section
          _buildFormSection(
            title: 'Mijoz Nomi',
            icon: Icons.person_outline,
            iconColor: Colors.red,
            child: Container(
              decoration: BoxDecoration(
                color: cBackgroundColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.red.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: TextFormField(
                controller: ticketName,
                style: const TextStyle(color: cWhite, fontSize: 16),
                enabled: widget.title == null,
                decoration: InputDecoration(
                  hintText: 'Mijoz nomini kiriting',
                  hintStyle: TextStyle(color: cGray, fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  prefixIcon: Icon(
                    Icons.person_outline,
                    color: cGray,
                    size: 20,
                  ),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Mijoz nomi kiritilishi kerak';
                  }
                  return null;
                },
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Amount Section
          _buildFormSection(
            title: 'Qarz Summasi',
            icon: Icons.payments_outlined,
            iconColor: Colors.orange,
            child: Column(
              children: [
                Container(
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: Colors.orange.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: TextFormField(
                    controller: number,
                    style: const TextStyle(color: cWhite, fontSize: 16),
                    keyboardType: TextInputType.number,
                    decoration: InputDecoration(
                      hintText: 'Summani kiriting',
                      hintStyle: TextStyle(color: cGray, fontSize: 14),
                      border: InputBorder.none,
                      contentPadding: const EdgeInsets.all(16),
                      prefixIcon: Icon(
                        Icons.payments_outlined,
                        color: cGray,
                        size: 20,
                      ),
                      suffixIcon: GestureDetector(
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(builder: (context) => Calculator()),
                          ).then((value) {
                            if (value != null) {
                              setState(() {
                                final formatted = NumberFormat("#,##0.##").format(double.parse(value));
                                final cleanValue = value.replaceAll(',', '');
                                final doubleValue = double.parse(cleanValue);
                                final intValue = doubleValue.toInt();

                                minus = temporaryValue - intValue;
                                add = temporaryValue + intValue;
                                number.value = TextEditingValue(
                                  text: formatted,
                                  selection: TextSelection.collapsed(offset: formatted.length),
                                );
                              });
                            }
                          });
                        },
                        child: Container(
                          margin: const EdgeInsets.all(8),
                          padding: const EdgeInsets.all(8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topLeft,
                              end: Alignment.bottomRight,
                              colors: [
                                Colors.orange.withOpacity(0.3),
                                Colors.orange.withOpacity(0.2),
                              ],
                            ),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.calculate_outlined,
                            color: Colors.orange,
                            size: 16,
                          ),
                        ),
                      ),
                    ),
                    validator: (value) {
                      if (value == null || value.isEmpty) {
                        return 'Summa kiritilishi kerak';
                      }
                      return null;
                    },
                    onChanged: (value) {
                      setState(() {
                        if (value.isEmpty) {
                          minus = temporaryValue;
                          add = temporaryValue;
                        } else {
                          final cleanValue = value.replaceAll(',', '');
                          try {
                            final intValue = int.parse(cleanValue);
                            minus = temporaryValue - intValue;
                            add = temporaryValue + intValue;

                            final formatted = NumberFormat("#,###").format(intValue);
                            number.value = TextEditingValue(
                              text: formatted,
                              selection: TextSelection.collapsed(offset: formatted.length),
                            );
                          } catch (e) {
                            // Handle parsing error
                          }
                        }
                      });
                    },
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Date Section
          _buildFormSection(
            title: 'Sana',
            icon: Icons.calendar_today_outlined,
            iconColor: Colors.blue,
            child: GestureDetector(
              onTap: () {
                showDatePicker(
                  initialDate: dateTime,
                  context: context,
                  firstDate: DateTime(2000),
                  lastDate: DateTime(2100),
                ).then((date) {
                  setState(() {
                    dateTime = date ?? DateTime.now();
                  });
                });
              },
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: cBackgroundColor.withOpacity(0.3),
                  borderRadius: BorderRadius.circular(12),
                  border: Border.all(
                    color: Colors.blue.withOpacity(0.3),
                    width: 1,
                  ),
                ),
                child: Row(
                  children: [
                    Icon(
                      Icons.calendar_today_outlined,
                      color: cGray,
                      size: 20,
                    ),
                    const SizedBox(width: 16),
                    Text(
                      formattedDate.format(dateTime),
                      style: const TextStyle(
                        color: cWhite,
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                    const Spacer(),
                    Icon(
                      Icons.arrow_drop_down,
                      color: cGray,
                      size: 24,
                    ),
                  ],
                ),
              ),
            ),
          ),

          const SizedBox(height: 24),

          // Comment Section
          _buildFormSection(
            title: 'Izoh (ixtiyoriy)',
            icon: Icons.comment_outlined,
            iconColor: Colors.green,
            child: Container(
              decoration: BoxDecoration(
                color: cBackgroundColor.withOpacity(0.3),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: Colors.green.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: TextField(
                controller: comment,
                style: const TextStyle(color: cWhite, fontSize: 16),
                maxLines: 3,
                decoration: InputDecoration(
                  hintText: 'Izoh yozing...',
                  hintStyle: TextStyle(color: cGray, fontSize: 14),
                  border: InputBorder.none,
                  contentPadding: const EdgeInsets.all(16),
                  prefixIcon: Padding(
                    padding: const EdgeInsets.only(top: 16, left: 16, right: 16),
                    child: Icon(
                      Icons.comment_outlined,
                      color: cGray,
                      size: 20,
                    ),
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildFormSection({
    required String title,
    required IconData icon,
    required Color iconColor,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: iconColor.withOpacity(0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                icon,
                color: iconColor,
                size: 18,
              ),
            ),
            const SizedBox(width: 12),
            Text(
              title,
              style: TextStyle(
                color: cWhite,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        child,
      ],
    );
  }



  Widget _buildNewDebtActions() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    color: cGray.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: cGray.withOpacity(0.3)),
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.close, color: cGray, size: 20),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              flex: 2,
              child: GestureDetector(
                onTap: _handleNewDebtSubmit,
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.red,
                        Colors.red.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.person_add, color: cWhite, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Qarz Berish',
                          style: TextStyle(
                            color: cWhite,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildExistingDebtActions() {
    return Column(
      children: [
        Row(
          children: [
            Expanded(
              child: GestureDetector(
                onTap: () => _handleDebtIncrease(),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.red,
                        Colors.red.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.red.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.add, color: cWhite, size: 20),
                        const SizedBox(height: 4),
                        Text(
                          'Qarz Qo\'shish',
                          style: TextStyle(
                            color: cWhite,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: GestureDetector(
                onTap: () => _handlePayment(),
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        Colors.green,
                        Colors.green.withOpacity(0.8),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.green.withOpacity(0.3),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Center(
                    child: Column(
                      children: [
                        Icon(Icons.remove, color: cWhite, size: 20),
                        const SizedBox(height: 4),
                        Text(
                          'To\'lov Olish',
                          style: TextStyle(
                            color: cWhite,
                            fontSize: 14,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
        const SizedBox(height: 16),
        GestureDetector(
          onTap: () => Navigator.pop(context),
          child: Container(
            width: double.infinity,
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: cGray.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: cGray.withOpacity(0.3)),
            ),
            child: Center(
              child: Text(
                'Bekor qilish',
                style: TextStyle(
                  color: cGray,
                  fontSize: 14,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  void _handleNewDebtSubmit() async {
    if (_formKey.currentState!.validate()) {
      bool result = await InternetConnectionChecker().hasConnection;
      if (result == true) {
        try {
          final amount = int.parse(number.text.replaceAll(',', ''));
          await secondDb.collection("calculation_list").add({
            "title": ticketName.text,
            "balance": amount, // Positive for debt amount
            "date": dateTime,
            "comment": comment.text,
            "add": false // false means debt increase
          }).then((value) {
            secondDb
                .collection("calculation_list")
                .doc(value.id)
                .collection("history")
                .add({
              "title": ticketName.text,
              "balance": amount,
              "date": dateTime,
              "comment": comment.text,
              "add": false
            });
            Navigator.pop(context);
            showTopSnackBar(
              Overlay.of(context),
              CustomSnackBar.success(
                message: "Yangi qarz yozuvi qo'shildi!",
              ),
            );
          });
        } catch (error) {
          showTopSnackBar(
            Overlay.of(context),
            CustomSnackBar.error(
              message: "Xatolik yuz berdi: $error",
            ),
          );
        }
      } else {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            message: "Internet aloqasi yo'q!",
          ),
        );
      }
    }
  }

  void _handleDebtIncrease() async {
    if (_formKey.currentState!.validate()) {
      bool result = await InternetConnectionChecker().hasConnection;
      if (result == true) {
        try {
          final amount = int.parse(number.text.replaceAll(',', ''));
          Map<String, dynamic> map = {
            "balance": add, // Add to existing debt
            "date": dateTime,
          };
          if (comment.text.isNotEmpty) {
            map['comment'] = comment.text;
          }

          await secondDb
              .collection("calculation_list")
              .doc(widget.id)
              .update(map)
              .then((value) {
            secondDb
                .collection("calculation_list")
                .doc(widget.id)
                .collection("history")
                .add({
              "title": ticketName.text,
              "balance": amount,
              "date": dateTime,
              "comment": comment.text,
              "add": false // false means debt increase
            });
            Navigator.pop(context);
            showTopSnackBar(
              Overlay.of(context),
              CustomSnackBar.success(
                message: "Qarz miqdori qo'shildi!",
              ),
            );
          });
        } catch (error) {
          showTopSnackBar(
            Overlay.of(context),
            CustomSnackBar.error(
              message: "Xatolik yuz berdi: $error",
            ),
          );
        }
      } else {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            message: "Internet aloqasi yo'q!",
          ),
        );
      }
    }
  }

  void _handlePayment() async {
    if (_formKey.currentState!.validate()) {
      bool result = await InternetConnectionChecker().hasConnection;
      if (result == true) {
        try {
          final amount = int.parse(number.text.replaceAll(',', ''));
          Map<String, dynamic> map = {
            "balance": minus, // Subtract from existing debt
            "date": dateTime,
          };
          if (comment.text.isNotEmpty) {
            map['comment'] = comment.text;
          }

          await secondDb
              .collection("calculation_list")
              .doc(widget.id)
              .update(map)
              .then((value) {
            secondDb
                .collection("calculation_list")
                .doc(widget.id)
                .collection("history")
                .add({
              "title": ticketName.text,
              "balance": amount,
              "date": dateTime,
              "comment": comment.text,
              "add": true // true means payment received
            });
            Navigator.pop(context);
            showTopSnackBar(
              Overlay.of(context),
              CustomSnackBar.success(
                message: "To'lov qabul qilindi!",
              ),
            );
          });
        } catch (error) {
          showTopSnackBar(
            Overlay.of(context),
            CustomSnackBar.error(
              message: "Xatolik yuz berdi: $error",
            ),
          );
        }
      } else {
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.error(
            message: "Internet aloqasi yo'q!",
          ),
        );
      }
    }
  }
}
