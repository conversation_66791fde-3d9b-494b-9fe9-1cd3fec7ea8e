import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../core/util/app_constants.dart';


class WorkerOrder extends StatefulWidget {
  const WorkerOrder({super.key});

  @override
  State<WorkerOrder> createState() => _WorkerOrderState();
}

class _WorkerOrderState extends State<WorkerOrder> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();
  var formattedDate = DateFormat('dd.MM.yyyy HH:mm');

  @override
  Widget build(BuildContext context) {
    print("owner_id ${getStorage.read("owner_id")}");
    print("work_type_id ${getStorage.read("work_type_id")}");
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Text(
          "Buyurtmalar",
          style: TextStyle(color: Colors.white),
        ),
        centerTitle: true,
      ),
      body: StreamBuilder(
          stream: secondDb
              .collection("work_orders")
              .where('owner_id', isEqualTo: getStorage.read('owner_id'))
              .where('work_id', isEqualTo: getStorage.read('work_type_id'))
              .snapshots(),
          builder: (BuildContext context, AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            if(snapshot.connectionState==ConnectionState.done||snapshot.connectionState==ConnectionState.active){
              return ListView.builder(
                  itemCount: snapshot.data?.size,
                  itemBuilder: (BuildContext context, int index) {
                    print(snapshot.data?.docs[index].data());
                    Map<String, dynamic> data =
                    snapshot.data?.docs[index].data() as Map<String, dynamic>;

                    return Container(
                      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                      margin: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: cItemColor),
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text("Buyurtma nomi: ${data["work_name"]}",style: TextStyle(color: Colors.white),),
                              Text(
                                  "Buyurtmalar soni: ${data["order_count"].toString()}",style: TextStyle(color: Colors.white)),
                              Text(formattedDate.format((data['date'].toDate())),
                                  style: TextStyle(
                                      color: Colors.white,
                                      fontWeight: FontWeight.w600))
                            ],
                          ),
                        ],
                      ),
                    );
                  });
            }
            else{
              return SizedBox();
            }

          }),
    );
  }
}
