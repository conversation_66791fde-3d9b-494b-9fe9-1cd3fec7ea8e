import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:market/wage_calculator/features/owner/worker_detail.dart';

import '../../../core/util/app_constants.dart';
import 'owner_home_bloc/owner_home_bloc.dart';



class OwnerHomePage extends StatefulWidget {
  final String workTypeId;
  final String workName;

  static Widget screen({required String workTypeId,required String workName}) {
    return BlocProvider(
      create: (context) => OwnerHomeBloc(),
      child: OwnerHomePage(workTypeId: workTypeId, workName: workName,),
    );
  }

  const OwnerHomePage({super.key, required this.workTypeId,required this.workName});

  @override
  State<OwnerHomePage> createState() => _OwnerHomePageState();
}

class _OwnerHomePageState extends State<OwnerHomePage> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();
  final formatterWithDecimals = NumberFormat("#,###");

  @override
  void initState() {
    super.initState();
    BlocProvider.of<OwnerHomeBloc>(context)
        .add(LoadOwnerEvent(workTypeId: widget.workTypeId));
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: cBackgroundColor,
        appBar: AppBar(
          iconTheme: IconThemeData(color: Colors.white),
          title: Text(
            widget.workName,
            style: TextStyle(color: Colors.white),
          ),
          backgroundColor: cAppBarColor,
        ),
        body: BlocConsumer<OwnerHomeBloc, OwnerHomeState>(
          listener: (context, state) {
          },
          builder: (context, state) {
            if(state is OwnerHomeInitial||state is OwnerHomeLoading){
              return Center(child: CupertinoActivityIndicator(color: Colors.white,),);
            }
            else if(state is OwnerHomeSuccess){
              print("---${state.items}");
              return ListView.builder(
                  itemCount: state.items.length,
                  itemBuilder: (BuildContext context, int index) {
                    return InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) =>
                                    WorkerDetail(
                                      fullname:state.items[index]['fullname'],
                                      id: state.items[index]['id'],
                                    )));
                      },
                      child: Container(
                        padding:
                        EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                        margin: EdgeInsets.all(6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: cItemColor),
                        width: MediaQuery
                            .of(context)
                            .size
                            .width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  state.items[index]['fullname'],
                                  style: TextStyle(
                                      fontWeight: FontWeight.bold,
                                      fontSize: 18,
                                      color: Colors.white),
                                ),
                                Row(
                                  children: [
                                    Text("Kasbi: [",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                            color: Colors.white)),
                                    Text(
                                        state.items[index]["work_name"],
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                            color: Colors.white)),
                                    Text("]",
                                        style: TextStyle(
                                            fontWeight: FontWeight.bold,
                                            fontSize: 14,
                                            color: Colors.white)),
                                  ],
                                )
                              ],
                            ),
                            Text(formatterWithDecimals.format(state.items[index]['total_sum']??0),style: TextStyle(color: Colors.white,fontSize: 18),)
                          ],
                        ),
                      ),
                    );
                  });
            }
            else {
              return SizedBox();}
          },
        )
    );
  }
}
