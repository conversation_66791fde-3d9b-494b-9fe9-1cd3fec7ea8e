import 'package:bloc/bloc.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:get_storage/get_storage.dart';

part 'report_event.dart';

part 'report_state.dart';

class ReportBloc extends Bloc<ReportEvent, ReportState> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();

  ReportBloc() : super(ReportInitial()) {
    on<LoadReportEvent>((event, emit) async {
      loadCollection();
    });
  }

  Future<void> loadCollection() async {
    emit(ReportLoading());
    try {
      num totalSum=0;
      List<Map<String, dynamic>> items = [];
      QuerySnapshot querySnapshot = await secondDb
          .collection('work_types')
          .where('owner_id',
              isEqualTo: getStorage.read('id')) // Optional: Add filters
          .get();

      for (QueryDocumentSnapshot doc in querySnapshot.docs) {
        Map<String, dynamic> map ={};
        Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
        map['work_type_id'] = doc.id;
        map['work_name'] = data['work_name'];
        QuerySnapshot querySnapshot = await secondDb
            .collection('worker')
            .where('owner_id', isEqualTo: getStorage.read('id'))
            .where('work_type_id', isEqualTo: [doc.id]).get();

        for (QueryDocumentSnapshot doc1 in querySnapshot.docs) {
          Map<String, dynamic> data1 = doc1.data() as Map<String, dynamic>;

          QuerySnapshot querySnapshot1 = await secondDb
              .collection("worker")
              .doc(doc1.id)
              .collection('calculation_list')
              .get();
          for (QueryDocumentSnapshot doc2 in querySnapshot1.docs) {
            Map<String, dynamic> data2 = doc2.data() as Map<String, dynamic>;
           totalSum+=data2['balance'];
           map['total_sum']=totalSum;
          }
        }
        items.add(map);
      }
      emit(ReportSuccess(items: items));
    } catch (e) {
      emit(ReportError(message: e.toString()));
    }
  }
}
