part of 'report_bloc.dart';

sealed class ReportState extends Equatable {
  const ReportState();
}

final class ReportInitial extends ReportState {
  @override
  List<Object> get props => [];
}

final class ReportLoading extends ReportState {
  @override
  List<Object> get props => [];
}

final class ReportSuccess extends ReportState {
  final List<Map<String, dynamic>> items;

  const ReportSuccess({required this.items});

  @override
  List<Object> get props => [];
}

final class ReportError extends ReportState {
  final String message;

  const ReportError({required this.message});

  @override
  List<Object> get props => [];
}
