import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../core/util/app_constants.dart';


class WorkerDetailHistory extends StatefulWidget {
 final String user_id;
 final String id;
 final String fullname;

  const WorkerDetailHistory({super.key, required this.user_id,required this.id, required this.fullname});

  @override
  State<WorkerDetailHistory> createState() => _WorkerDetailHistoryState();
}

class _WorkerDetailHistoryState extends State<WorkerDetailHistory> {

  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();
  var formattedDate = DateFormat('dd.MM.yyyy HH:mm');
  final formatterWithDecimals = NumberFormat("#,###");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Text(
         "Tarix",
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: StreamBuilder(
          stream: secondDb
              .collection('worker')
              .doc(widget.user_id)
              .collection('calculation_list')
              .doc(widget.id)
              .collection("history")
              .orderBy("date", descending: true)
              .snapshots(),
          builder: (BuildContext context,
              AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            if(snapshot.connectionState==ConnectionState.active||snapshot.connectionState==ConnectionState.done){
              return ListView.builder(
                  itemCount: snapshot.data?.size,
                  itemBuilder: (BuildContext context, int index) {
                    Map<String, dynamic> data =
                    snapshot.data?.docs[index].data() as Map<String, dynamic>;
                    print(data['date'].toString());
                    return Container(
                      padding: EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                      margin: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color:cItemColor),
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        children: [
                          Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    data['workName'],
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold),
                                  ),
                                  Text(data['comment'],
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600)),
                                  Text(formattedDate.format((data['date'].toDate())),
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.w600))
                                ],
                              )),
                          Expanded(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.end,
                                children: [
                                  Text(
                                    " ${data['add'] == true ? "+" : "-"} ${formatterWithDecimals.format(data['balance']??0)}",
                                    style: TextStyle(
                                        fontSize: 20,
                                        color: data['add'] == true
                                            ? Colors.green
                                            : Colors.red,
                                        fontWeight: FontWeight.bold),
                                  )
                                ],
                              )),
                        ],
                      ),
                    );
                  });
            }
            else{
              return SizedBox();
            }
          }),
    );
  }

}
