import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../core/util/app_constants.dart';
import 'add_worker_page.dart';



class WorkerControllerPage extends StatefulWidget {
  const WorkerControllerPage({super.key});

  @override
  State<WorkerControllerPage> createState() => _WorkerControllerPageState();
}

class _WorkerControllerPageState extends State<WorkerControllerPage> {
  static FirebaseFirestore get secondDb =>
      FirebaseFirestore.instanceFor(
          app: Firebase.app(), databaseURL: 'wage-calculation');
  final getStorage = GetStorage();

  void _showDeleteConfirmationDialog(
      {required BuildContext context, required String id}) {
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return AlertDialog(
          title: const Text('Delete History'),
          content:
          const Text('Are you sure you want to delete this history item?'),
          actions: [
            TextButton(
              onPressed: () {
                Navigator.of(context).pop(); // Close dialog without deleting
              },
              child: const Text('No'),
            ),
            TextButton(
              onPressed: () {
                secondDb.collection('worker').doc(id).collection(
                    'calculation_list').doc().delete();
                secondDb.collection('worker').doc(id).delete();
                Navigator.of(context).pop(); // Close dialog after deleting
              },
              child: const Text('Yes'),
            ),
          ],
        );
      },
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Center(
          child: Text(
            "Ishchi Sozlamalari",
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
      body: StreamBuilder(
          stream: secondDb
              .collection('worker')
              .where("owner_id", isEqualTo: getStorage.read("id"))
              .snapshots(),
          builder: (BuildContext context,
              AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            if (snapshot.connectionState == ConnectionState.active ||
                snapshot.connectionState == ConnectionState.done) {
              return ListView.builder(
                  itemCount: snapshot.data?.size,
                  itemBuilder: (BuildContext context, int index) {
                    Map<String, dynamic> data = snapshot.data?.docs[index]
                        .data() as Map<String, dynamic>;
                    return Slidable(
                      endActionPane:
                      ActionPane(motion: ScrollMotion(), children: [
                        SlidableAction(
                          flex: 1,
                          onPressed: (value) =>
                              _showDeleteConfirmationDialog(
                                context: context,
                                id: snapshot.data!.docs[index].id,
                              ),
                          backgroundColor: Colors.red,
                          foregroundColor: Colors.white,
                          icon: Icons.delete,
                          label: 'Delete',
                        )
                      ]),
                      child: Container(
                        padding:
                        EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                        margin: EdgeInsets.all(6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: cItemColor),
                        width: MediaQuery
                            .of(context)
                            .size
                            .width,
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            Text(
                              "${data['worker_name']} ${data['worker_surname']}",
                              style: TextStyle(color: Colors.white),
                            ),
                            Row(
                              children: [
                                Text("Kasbi: [",
                                    style: TextStyle(color: Colors.white)),
                                StreamBuilder(
                                    stream: secondDb
                                        .collection('work_types')
                                        .doc(data['work_type_id'][0])
                                        .snapshots(),
                                    builder: (context, snapshot) {
                                      if (snapshot.connectionState ==
                                          ConnectionState.active ||
                                          snapshot.connectionState ==
                                              ConnectionState.done) {
                                        return Text(
                                            snapshot.data!.data()?["work_name"],
                                            style:
                                            TextStyle(color: Colors.white));
                                      } else {
                                        return SizedBox();
                                      }
                                    }),
                                Text("]",
                                    style: TextStyle(color: Colors.white)),
                              ],
                            )
                          ],
                        ),
                      ),
                    );
                  });
            } else {
              return SizedBox();
            }
          }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(context,
              MaterialPageRoute(builder: (context) => AddWorkerPage()));
        },
        backgroundColor: cButtonColor,
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
    );
  }
}
