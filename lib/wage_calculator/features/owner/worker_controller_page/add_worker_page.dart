import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../../core/network/network_info.dart';
import '../../../../core/util/app_constants.dart';

class AddWorkerPage extends StatefulWidget {
  const AddWorkerPage({super.key});

  @override
  State<AddWorkerPage> createState() => _AddWorkerPageState();
}

class _AddWorkerPageState extends State<AddWorkerPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController name = TextEditingController();
  TextEditingController surname = TextEditingController();
  TextEditingController login = TextEditingController();
  TextEditingController parol = TextEditingController();
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();
  final NetworkInfo networkInfo = NetworkInfoImpl(InternetConnectionChecker());

  String? selectedValue;
  final TextEditingController textEditingController = TextEditingController();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Center(
          child: Text(
            "Ishchi qo'shish",
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextFormField(
                  controller: name,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: "Ishchining ismi",
                    labelStyle: TextStyle(color: Colors.white),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
              SizedBox(
                height: 10,
              ),
              TextFormField(
                  controller: surname,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelStyle: TextStyle(color: Colors.white),
                    labelText: "Ishchining familyasi",
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
              SizedBox(
                height: 10,
              ),
              TextFormField(
                  controller: login,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelStyle: TextStyle(color: Colors.white),
                    labelText: "Login",
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
              SizedBox(
                height: 10,
              ),
              TextFormField(
                  controller: parol,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: "Parol",
                    labelStyle: TextStyle(color: Colors.white),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
              SizedBox(
                height: 10,
              ),
              StreamBuilder(
                  stream: secondDb
                      .collection("work_types")
                      .where('owner_id', isEqualTo: getStorage.read('id'))
                      .snapshots(),
                  builder: (context, snapshot) {
                    return Container(
                      padding:
                      EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(width: 1, color: Colors.white)),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton2<String>(
                          isExpanded: true,
                          hint: Text(
                            'Ishchi kasbini tanlang',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                          items: snapshot.data!.docs
                              .map((doc) => DropdownMenuItem(
                            value: doc.id, // Use document ID as value
                            child: Text(
                              (doc.data())['work_name'],
                              style: const TextStyle(
                                  fontSize: 14, color: Colors.green),
                            ),
                          ))
                              .toList(),
                          value: selectedValue,
                          onChanged: (value) {
                            setState(() {
                              selectedValue = value;
                            });
                          },
                          buttonStyleData: const ButtonStyleData(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            height: 40,
                            width: 200,
                          ),
                          dropdownStyleData: const DropdownStyleData(
                            maxHeight: 200,
                          ),
                          menuItemStyleData: const MenuItemStyleData(
                            height: 40,
                          ),
                          dropdownSearchData: DropdownSearchData(
                            searchController: textEditingController,
                            searchInnerWidgetHeight: 50,
                            searchInnerWidget: Container(
                              height: 50,
                              padding: const EdgeInsets.only(
                                top: 8,
                                bottom: 4,
                                right: 8,
                                left: 8,
                              ),
                              child: TextFormField(
                                expands: true,
                                maxLines: null,
                                controller: textEditingController,
                                decoration: InputDecoration(
                                  isDense: true,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 8,
                                  ),
                                  hintText: 'Ishchi kasbini qidirish...',
                                  hintStyle: const TextStyle(fontSize: 12),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                            searchMatchFn: (item, searchValue) {
                              return item.value
                                  .toString()
                                  .contains(searchValue);
                            },
                          ),
                          //This to clear the search value when you close the menu
                          onMenuStateChange: (isOpen) {
                            if (!isOpen) {
                              textEditingController.clear();
                            }
                          },
                        ),
                      ),
                    );
                  }),
              SizedBox(
                height: 40,
              ),
              MaterialButton(
                height: 40,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                minWidth: MediaQuery.of(context).size.width,
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    if (await networkInfo.isConnected) {
                      if (selectedValue != null) {
                        secondDb.collection('worker').add({
                          "worker_name": name.text,
                          "worker_surname": surname.text,
                          "login": login.text,
                          "password": parol.text,
                          "work_type_id": [selectedValue],
                          "owner_id": getStorage.read('id')
                        }).then((DocumentReference doc) {
                          Navigator.pop(context);
                          print("Document added with ID: ${doc.id}");
                          // Operation successful
                        }).catchError((error) {
                          showTopSnackBar(
                            Overlay.of(context),
                            CustomSnackBar.error(
                              message: "Error adding document: $error",
                            ),
                          );
                          // Handle error
                        });
                      } else {
                        showTopSnackBar(
                          Overlay.of(context),
                          CustomSnackBar.error(
                            message: "Ishchi kasbini tanlashingiz kerak !",
                          ),
                        );
                      }
                    } else {}
                  }
                },
                color: cButtonColor,
                child: Text(
                  "" == null ? "Saqlash" : "Tahrirlash",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
