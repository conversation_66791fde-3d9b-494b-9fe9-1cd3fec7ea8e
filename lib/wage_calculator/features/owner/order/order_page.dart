import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../../../core/util/app_constants.dart';
import 'order_add_page.dart';


class OrderPage extends StatefulWidget {
  const OrderPage({super.key});

  @override
  State<OrderPage> createState() => _OrderPageState();
}

class _OrderPageState extends State<OrderPage> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
  );
  final getStorage = GetStorage();
  var formattedDate = DateFormat('dd.MM.yyyy HH:mm');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        backgroundColor: cBackgroundColor,
        appBar: AppBar(
            iconTheme: IconThemeData(color: Colors.white),
            backgroundColor: cAppBarColor,
            title: Text(
              "Ishlar",
              style: TextStyle(color: Colors.white),
            )),
        body: StreamBuilder(
            stream: secondDb
                .collection("work_orders")
                .where('owner_id', isEqualTo: getStorage.read('id'))
                .snapshots(),
            builder: (BuildContext context,
                AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
              if (snapshot.connectionState == ConnectionState.done||snapshot.connectionState == ConnectionState.active) {
                return ListView.builder(
                    itemCount: snapshot.data?.size,
                    itemBuilder: (BuildContext context, int index) {
                      Map<String, dynamic> data = snapshot.data?.docs[index]
                          .data() as Map<String, dynamic>;
                      return Container(
                        padding:
                            EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                        margin: EdgeInsets.all(6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: cItemColor),
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                Text(
                                  "Buyurtma nomi: ${data["work_name"]}",
                                  style: TextStyle(color: Colors.white),
                                ),
                                Text(
                                    "Buyurtmalar soni: ${data["order_count"].toString()}",
                                    style: TextStyle(color: Colors.white)),
                                Text(
                                    formattedDate
                                        .format((data['date'].toDate())),
                                    style: TextStyle(
                                        color: Colors.white,
                                        fontWeight: FontWeight.w600))
                              ],
                            ),
                            Column(
                              children: [
                                IconButton(
                                    onPressed: () {
                                      Navigator.push(
                                          context,
                                          MaterialPageRoute(
                                              builder: (context) =>
                                                  OrderAddPage(
                                                    id: snapshot
                                                        .data?.docs[index].id,
                                                    work_name:
                                                        data["work_name"],
                                                    work_id: data['work_id'],
                                                    order_count:
                                                        data['order_count'],
                                                    order_desciption: data[
                                                        'order_description'],
                                                  )));
                                      data['id'] =
                                          snapshot.data?.docs[index].id;
                                    },
                                    icon: Icon(Icons.edit,color: Colors.white,)),
                                IconButton(
                                    onPressed: () {
                                      showDialog(
                                          context: context,
                                          builder: (context) {
                                            return AlertDialog(
                                              title: Text("Ogohlantirish!"),
                                              content: Text(
                                                  "Rostan ham ushbu ishni o'chirmoqchimisiz?"),
                                              actions: [
                                                TextButton(
                                                    onPressed: () {
                                                      Navigator.pop(context);
                                                    },
                                                    child: Text("Yo'q")),
                                                TextButton(
                                                    onPressed: () {
                                                      secondDb
                                                          .collection(
                                                              "work_orders")
                                                          .doc(snapshot.data
                                                              ?.docs[index].id)
                                                          .delete()
                                                          .then((value) {
                                                        Navigator.pop(context);
                                                      });
                                                    },
                                                    child: Text("Ha")),
                                              ],
                                            );
                                          });
                                    },
                                    icon: Icon(Icons.delete,color: Colors.white,)),
                              ],
                            ),
                          ],
                        ),
                      );
                    });
              } else {
                return SizedBox();
              }
            }),
        floatingActionButton: FloatingActionButton(
          backgroundColor: cButtonColor,
          onPressed: () {
            Navigator.push(context,
                MaterialPageRoute(builder: (context) => OrderAddPage()));
          },
          child: Icon(
            Icons.add,
            color: Colors.white,
          ),
        ));
  }
}
