import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:dropdown_button2/dropdown_button2.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../../core/network/network_info.dart';
import '../../../../core/util/app_constants.dart';
class OrderAddPage extends StatefulWidget {
  final String? id;
  final int? order_count;
  final String? order_desciption;
  final String? owner_id;
  final String? work_id;
  final String? work_name;

  const OrderAddPage(
      {super.key,
      this.id,
      this.order_count,
      this.order_desciption,
      this.owner_id,
      this.work_id,
      this.work_name});

  @override
  State<OrderAddPage> createState() => _OrderAddPageState();
}

class _OrderAddPageState extends State<OrderAddPage> {
  final _formKey = GlobalKey<FormState>();
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app()
  );
  final getStorage = GetStorage();
  final NetworkInfo networkInfo = NetworkInfoImpl(InternetConnectionChecker());
  TextEditingController workName = TextEditingController();
  TextEditingController workCount = TextEditingController();
  TextEditingController workDescription = TextEditingController();
  Work? selectedValue;
  TextEditingController textEditingController = TextEditingController();

  @override
  void initState() {
    super.initState();
    if (widget.id != null) {
      selectedValue =
          Work(workName: widget.work_name ?? "", workId: widget.work_id ?? "");
      workName = TextEditingController(text: widget.work_name);
      workDescription = TextEditingController(text: widget.order_desciption);
      workCount = TextEditingController(text: widget.order_count.toString());
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Text(
          "Ish qo'shish",
          style: TextStyle(color: Colors.white),
        ),
        centerTitle: true,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              StreamBuilder(
                  stream: secondDb
                      .collection("work_types")
                      .where('owner_id', isEqualTo: getStorage.read('id'))
                      .snapshots(),
                  builder: (context, snapshot) {
                    List<Work>? workTypes = snapshot.data?.docs.map((doc) {
                      return Work(
                          workName: doc.data()['work_name'], workId: doc.id);
                    }).toList();
                    return Container(
                      padding: EdgeInsets.symmetric(horizontal: 0, vertical: 8),
                      width: MediaQuery.of(context).size.width,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(width: 1, color: Colors.white)),
                      child: DropdownButtonHideUnderline(
                        child: DropdownButton2<Work>(
                          isExpanded: true,
                          hint: Text(
                            'Ishchi kasbini tanlang',
                            style: TextStyle(
                              fontSize: 14,
                              color: Colors.white,
                            ),
                          ),
                          items: workTypes
                              ?.map((e) => DropdownMenuItem<Work>(
                                    value: e, // Use document ID as value
                                    child: Text(
                                      e.workName,
                                      style: const TextStyle(
                                          fontSize: 14, color: Colors.green),
                                    ),
                                  ))
                              .toList(),
                          value: selectedValue,
                          onChanged: (value) {
                            setState(() {
                              selectedValue = value;
                            });
                          },
                          buttonStyleData: const ButtonStyleData(
                            padding: EdgeInsets.symmetric(horizontal: 16),
                            height: 40,
                            width: 200,
                          ),
                          dropdownStyleData: const DropdownStyleData(
                            maxHeight: 200,
                          ),
                          menuItemStyleData: const MenuItemStyleData(
                            height: 40,
                          ),
                          dropdownSearchData: DropdownSearchData(
                            searchController: textEditingController,
                            searchInnerWidgetHeight: 50,
                            searchInnerWidget: Container(
                              height: 50,
                              padding: const EdgeInsets.only(
                                top: 8,
                                bottom: 4,
                                right: 8,
                                left: 8,
                              ),
                              child: TextFormField(
                                expands: true,
                                maxLines: null,
                                controller: textEditingController,
                                decoration: InputDecoration(
                                  isDense: true,
                                  contentPadding: const EdgeInsets.symmetric(
                                    horizontal: 10,
                                    vertical: 8,
                                  ),
                                  hintText: 'Ishchi kasbini qidirish...',
                                  hintStyle: const TextStyle(fontSize: 12),
                                  border: OutlineInputBorder(
                                    borderRadius: BorderRadius.circular(8),
                                  ),
                                ),
                              ),
                            ),
                            searchMatchFn: (item, searchValue) {
                              return item.value
                                  .toString()
                                  .contains(searchValue);
                            },
                          ),
                          //This to clear the search value when you close the menu
                          onMenuStateChange: (isOpen) {
                            if (!isOpen) {
                              textEditingController.clear();
                            }
                          },
                        ),
                      ),
                    );
                  }),
              SizedBox(
                height: 10,
              ),
              TextFormField(
                  controller: workCount,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: "Ish soni",
                    labelStyle: TextStyle(color: Colors.white),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20)),
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
              SizedBox(
                height: 20,
              ),
              SizedBox(
                height: 200,
                child: TextFormField(
                  style: TextStyle(color: Colors.white),
                  controller: workDescription,
                  maxLines: null,
                  expands: true,
                  decoration: InputDecoration(
                    labelStyle: TextStyle(color: Colors.white),
                    labelText: "Ish Tarifi",
                    alignLabelWithHint: true,
                    contentPadding: EdgeInsets.only(
                        left: 10, top: 10, right: 10, bottom: 10),
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(20),
                    ),
                  ),
                  textAlignVertical: TextAlignVertical.top,
                  // This makes text start from top
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  },
                ),
              ),
              SizedBox(
                height: 10,
              ),
              SizedBox(
                height: 10,
              ),
              MaterialButton(
                height: 40,
                shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(20)),
                minWidth: MediaQuery.of(context).size.width,
                onPressed: () async {
                  if (_formKey.currentState!.validate()) {
                    if (await networkInfo.isConnected) {
                      if (widget.id != null) {
                        secondDb
                            .collection("work_orders")
                            .doc(widget.id)
                            .update({
                          "owner_id": getStorage.read("id"),
                          "work_id": selectedValue?.workId ?? "",
                          "work_name": selectedValue?.workName ?? "",
                          "order_count": int.parse(workCount.text),
                          "order_description": workDescription.text,
                          "date": DateTime.now()
                        }).then((value) {
                          Navigator.pop(context);
                        });
                      } else {
                        secondDb.collection("work_orders").add({
                          "owner_id": getStorage.read("id"),
                          "work_id": selectedValue?.workId ?? "",
                          "work_name": selectedValue?.workName ?? "",
                          "order_count": int.parse(workCount.text),
                          "order_description": workDescription.text,
                          "date": DateTime.now()
                        }).then((value) {
                          Navigator.pop(context);
                        });
                      }
                    } else {
                      showTopSnackBar(
                        Overlay.of(context),
                        CustomSnackBar.error(
                          message: "Internet bilan aloqa yo'q !",
                        ),
                      );
                    }
                  }
                },
                color: cButtonColor,
                child: Text(
                  widget.id == null ? "Saqlash" : "Tahrirlash",
                  style: TextStyle(color: Colors.white),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class Work {
  String workName;
  String workId;

  Work({required this.workName, required this.workId});

  // Equality operator
  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is Work &&
          runtimeType == other.runtimeType &&
          workName == other.workName &&
          workId == other.workId;

  @override
  int get hashCode => workName.hashCode ^ workId.hashCode;

  // Convert a JSON map into a Work object
  factory Work.fromJson(Map<String, dynamic> json) {
    return Work(
      workName: json['workName'] as String,
      workId: json['workId'] as String,
    );
  }

  // Convert a Work object into a JSON map
  Map<String, dynamic> toJson() {
    return {
      'workName': workName,
      'workId': workId,
    };
  }

  @override
  String toString() {
    return 'Work{workName: $workName, workId: $workId}';
  }
}
