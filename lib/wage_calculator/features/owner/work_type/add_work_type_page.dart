import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../../core/network/network_info.dart';
import '../../../../core/util/app_constants.dart';


class AddWorkTypePage extends StatefulWidget {
  final Map<String, dynamic>? map;

  const AddWorkTypePage({super.key, this.map});

  @override
  State<AddWorkTypePage> createState() => _AddWorkTypePageState();
}

class _AddWorkTypePageState extends State<AddWorkTypePage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController text = TextEditingController();
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(), databaseURL: 'wage-calculation');
  final getStorage = GetStorage();
  final NetworkInfo networkInfo = NetworkInfoImpl(InternetConnectionChecker());

  @override
  void initState() {
    super.initState();
    if (widget.map != null) {
      text = TextEditingController(text: widget.map?['work_name']);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        title: Text(
          "Ish turini qo'shish",
          style: TextStyle(color: Colors.white),
        ),
        backgroundColor: cAppBarColor,
      ),
      body: Padding(
        padding: const EdgeInsets.all(8.0),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Form(
              key: _formKey,
              child: TextFormField(
                  controller: text,
                  style: TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    labelText: "Ish nomini kiritish",
                    labelStyle: TextStyle(color: Colors.white),
                    border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(20),
                        borderSide: BorderSide(color: Colors.white)),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'Ma`lumot kiritilishi kerak';
                    }
                    return null;
                  }),
            ),
            SizedBox(
              height: 40,
            ),
            MaterialButton(
              height: 40,
              shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(20)),
              minWidth: MediaQuery.of(context).size.width,
              onPressed: () async {
                if (_formKey.currentState!.validate()) {
                  if (widget.map == null) {
                    if (await networkInfo.isConnected) {
                      QuerySnapshot querySnapshot = await secondDb
                          .collection('work_types')
                          .where('owner_id', isEqualTo: getStorage.read("id"))
                          .where('work_name', isEqualTo: text.text)
                          .get();
                      if (querySnapshot.docs.isEmpty) {
                        secondDb.collection("work_types").add({
                          "owner_id": getStorage.read("id"),
                          "work_name": text.text
                        });
                        Navigator.pop(context);
                      } else {
                        showTopSnackBar(
                          Overlay.of(context),
                          CustomSnackBar.error(
                            message: "Bu nomli kasb allaqachon kiritilgan!",
                          ),
                        );
                      }
                    } else {
                      showTopSnackBar(
                        Overlay.of(context),
                        CustomSnackBar.error(
                          message: "Internet bilan aloqa yo'q",
                        ),
                      );
                    }
                  } else {
                    if (await networkInfo.isConnected) {
                      QuerySnapshot querySnapshot = await secondDb
                          .collection('work_types')
                          .where('owner_id', isEqualTo: getStorage.read("id"))
                          .where('work_name', isEqualTo: text.text)
                          .get();
                      if (querySnapshot.docs.isEmpty) {
                        secondDb
                            .collection("work_types")
                            .doc(widget.map?['id'])
                            .update({
                          "owner_id": getStorage.read("id"),
                          "work_name": text.text
                        });
                        Navigator.pop(context);
                      } else {
                        showTopSnackBar(
                          Overlay.of(context),
                          CustomSnackBar.error(
                            message: "Bu nomli kasb allaqachon kiritilgan!",
                          ),
                        );
                      }
                    } else {
                      showTopSnackBar(
                        Overlay.of(context),
                        CustomSnackBar.error(
                          message: "Internet bilan aloqa yo'q",
                        ),
                      );
                    }
                  }
                }
              },
              color: cButtonColor,
              child: Text(
                widget.map == null ? "Saqlash" : "Tahrirlash",
                style: TextStyle(color: Colors.white),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
