import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';

import '../../../../core/util/app_constants.dart';
import 'add_work_type_page.dart';


class WorkTypePage extends StatefulWidget {
  const WorkTypePage({super.key});

  @override
  State<WorkTypePage> createState() => _WorkTypePageState();
}

class _WorkTypePageState extends State<WorkTypePage> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Text(
          "<PERSON><PERSON>",
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: StreamBuilder(
          stream: secondDb
              .collection("work_types")
              .where('owner_id', isEqualTo: getStorage.read('id'))
              .snapshots(),
          builder: (BuildContext context,
              AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            if (snapshot.connectionState == ConnectionState.active ||
                snapshot.connectionState == ConnectionState.done) {
              return ListView.builder(
                  itemCount: snapshot.data?.size,
                  itemBuilder: (BuildContext context, int index) {
                    Map<String, dynamic> data = snapshot.data?.docs[index]
                        .data() as Map<String, dynamic>;
                    return Container(
                      padding:
                          EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                      margin: EdgeInsets.all(6),
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(12),
                          color: cItemColor),
                      width: MediaQuery.of(context).size.width,
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            data["work_name"],
                            style: TextStyle(color: Colors.white),
                          ),
                          IconButton(
                              onPressed: () {
                                data['id'] = snapshot.data?.docs[index].id;
                                Navigator.push(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => AddWorkTypePage(
                                              map: data,
                                            )));
                              },
                              icon: Icon(
                                Icons.edit,
                                color: Colors.white,
                              ))
                        ],
                      ),
                    );
                  });
            } else {
              return SizedBox();
            }
          }),
      floatingActionButton: FloatingActionButton(
        onPressed: () {
          Navigator.push(context,
              MaterialPageRoute(builder: (context) => AddWorkTypePage()));
        },
        backgroundColor: cButtonColor,
        child: Icon(
          Icons.add,
          color: Colors.white,
        ),
      ),
    );
  }
}
