import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:market/wage_calculator/features/owner/worker_detail_history.dart';

import '../../../core/util/app_constants.dart';



class WorkerDetail extends StatefulWidget {
  final String fullname;
  final String id;

  const WorkerDetail(
      {super.key,
      required this.fullname,
      required this.id});

  @override
  State<WorkerDetail> createState() => _WorkerDetailState();
}

class _WorkerDetailState extends State<WorkerDetail> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();
  final formatterWithDecimals = NumberFormat("#,###");

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        iconTheme: IconThemeData(color: Colors.white),
        backgroundColor: cAppBarColor,
        title: Text(
          widget.fullname,
          style: TextStyle(color: Colors.white),
        ),
      ),
      body: StreamBuilder(
          stream: secondDb
              .collection('worker')
              .doc(widget.id)
              .collection('calculation_list')
              .orderBy("date", descending: false)
              .snapshots(),
          builder: (BuildContext context,
              AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
            if(snapshot.connectionState==ConnectionState.active||snapshot.connectionState==ConnectionState.done){
              return ListView.builder(
                  itemCount: snapshot.data?.size,
                  itemBuilder: (BuildContext context, int index) {
                    Map<String, dynamic> data =
                    snapshot.data?.docs[index].data() as Map<String, dynamic>;
                    return InkWell(
                      onTap: () {
                        Navigator.push(
                            context,
                            MaterialPageRoute(
                                builder: (context) => WorkerDetailHistory(
                                  id: snapshot.data!.docs[index].id,
                                  user_id: widget.id,
                                  fullname: widget.fullname,
                                )));
                      },
                      child: Container(
                        padding:
                        EdgeInsets.symmetric(horizontal: 20, vertical: 20),
                        margin: EdgeInsets.all(6),
                        decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(12),
                            color: cItemColor),
                        width: MediaQuery.of(context).size.width,
                        child: Row(
                          children: [
                            Expanded(
                                child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Text(
                                      data['workName'],
                                      style: TextStyle(
                                          color: Colors.white,
                                          fontWeight: FontWeight.bold,
                                          fontSize: 18),
                                    ),
                                    Text(data['comment'],
                                        style: TextStyle(
                                            color: Colors.white,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 12))
                                  ],
                                )),
                            Expanded(
                                child: Row(
                                  mainAxisAlignment: MainAxisAlignment.end,
                                  children: [
                                    Text(formatterWithDecimals.format(data['balance']??0),
                                        style: TextStyle(
                                            color: data['balance'] < 0
                                                ? Colors.red
                                                : Colors.green,
                                            fontWeight: FontWeight.bold,
                                            fontSize: 18))
                                  ],
                                )),
                          ],
                        ),
                      ),
                    );
                  });
            }
            else{
              return SizedBox();
            }
          }),
    );
  }
}
