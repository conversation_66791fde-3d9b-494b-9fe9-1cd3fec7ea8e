import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';
import 'package:market/wage_calculator/features/owner/report_bloc/report_bloc.dart';
import 'package:market/wage_calculator/features/owner/work_type/work_type_page.dart';
import 'package:market/wage_calculator/features/owner/worker_controller_page/worker_controller_page.dart';

import '../../../core/util/app_constants.dart';
import '../../../generated/assets.dart';
import '../login/login_page.dart';
import '../login_settings.dart';
import 'order/order_page.dart';
import 'owner_home_page.dart';

class ReportPage extends StatefulWidget {
  static Widget screen() {
    return BlocProvider(
      create: (context) => ReportBloc(),
      child: ReportPage(),
    );
  }

  const ReportPage({super.key});

  @override
  State<ReportPage> createState() => _ReportPageState();
}

class _ReportPageState extends State<ReportPage> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(), databaseURL: 'wage-calculation');
  final getStorage = GetStorage();
  final formatterWithDecimals = NumberFormat("#,###");

  @override
  void initState() {
    super.initState();
    BlocProvider.of<ReportBloc>(context).add(LoadReportEvent());
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
          backgroundColor: cAppBarColor,
          title: Center(
            child: Text(
              "Ish Turi",
              style: TextStyle(color: Colors.white),
            ),
          ),
          actions: [
            PopupMenuButton<String>(
              // iconColor: Colors.white,
              onSelected: (String value) {
                if (value == "Ishchi qo'shish") {
                  Navigator.push(
                      context,
                      MaterialPageRoute(
                          builder: (context) => WorkerControllerPage()));
                } else if (value == "Ish turini qo'shish") {
                  Navigator.push(context,
                      MaterialPageRoute(builder: (context) => WorkTypePage()));
                } else if (value == "Login sozlamalar") {
                  Navigator.push(context,
                      MaterialPageRoute(builder: (context) => LoginSettings()));
                } else if (value == "Chiqish") {
                  getStorage.erase();
                  Navigator.pushAndRemoveUntil(
                      context,
                      MaterialPageRoute(builder: (context) => LoginPage()),
                      (Route<dynamic> route) => false);
                }
              },
              itemBuilder: (BuildContext context) {
                return {
                  "Ishchi qo'shish",
                  "Ish turini qo'shish",
                  "Login sozlamalar",
                  "Chiqish"
                }.map((String choice) {
                  return PopupMenuItem<String>(
                    value: choice,
                    child: Text(choice),
                  );
                }).toList();
              },
            ),
          ]),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: InkWell(
                onTap: () {
                  Navigator.push(context,
                      MaterialPageRoute(builder: (context) => OrderPage()));
                },
                child: SizedBox(
                    height: 150,
                    width: double.infinity,
                    child: Image.asset(
                      Assets.assetsOrder,
                      fit: BoxFit.cover,
                    )),
              ),
            ),
          ),
          Expanded(
            child: BlocBuilder<ReportBloc, ReportState>(
              builder: (context, state) {
                if (state is ReportInitial || state is ReportLoading) {
                  return Center(
                    child: CupertinoActivityIndicator(
                      color: Colors.white,
                    ),
                  );
                } else if (state is ReportSuccess) {
                  return ListView.builder(
                      itemCount: state.items.length,
                      itemBuilder: (BuildContext context, int index) {
                        return InkWell(
                          onTap: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => OwnerHomePage.screen(
                                          workTypeId: state.items[index]
                                                  ['work_type_id'] ??
                                              "",
                                          workName: state.items[index]
                                                  ['work_name'] ??
                                              "",
                                        )));
                          },
                          child: Container(
                            padding: EdgeInsets.symmetric(
                                horizontal: 20, vertical: 20),
                            margin: EdgeInsets.all(6),
                            decoration: BoxDecoration(
                                borderRadius: BorderRadius.circular(12),
                                color: cItemColor),
                            width: MediaQuery.of(context).size.width,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              children: [
                                Text(
                                  state.items[index]['work_name'] ?? "",
                                  style: TextStyle(color: Colors.white),
                                ),
                                Text(
                                  formatterWithDecimals.format(
                                      state.items[index]['total_sum'] ?? 0),
                                  style: TextStyle(color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        );
                      });
                } else {
                  return SizedBox();
                }
              },
            ),
          )
          // Expanded(
          //   child: StreamBuilder(
          //       stream: firestore
          //           .collection("work_types")
          //           .where('owner_id', isEqualTo: getStorage.read('id'))
          //           .snapshots(),
          //       builder: (BuildContext context,
          //           AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
          //         if (snapshot.connectionState == ConnectionState.done ||
          //             snapshot.connectionState == ConnectionState.active) {
          //           return ListView.builder(
          //               itemCount: snapshot.data?.size,
          //               itemBuilder: (BuildContext context, int index) {
          //                 Map<String, dynamic> data = snapshot.data?.docs[index]
          //                     .data() as Map<String, dynamic>;
          //                 return InkWell(
          //                   onTap: () {
          //                     Navigator.push(
          //                         context,
          //                         MaterialPageRoute(
          //                             builder: (context) => OwnerHomePage(
          //                                   workTypeId:
          //                                       snapshot.data?.docs[index].id ??
          //                                           "",
          //                                 )));
          //                   },
          //                   child: Container(
          //                     padding: EdgeInsets.symmetric(
          //                         horizontal: 20, vertical: 20),
          //                     margin: EdgeInsets.all(6),
          //                     decoration: BoxDecoration(
          //                         borderRadius: BorderRadius.circular(12),
          //                         color: cItemColor),
          //                     width: MediaQuery.of(context).size.width,
          //                     child: Row(
          //                       mainAxisAlignment:
          //                           MainAxisAlignment.spaceBetween,
          //                       children: [
          //                         Text(
          //                           data["work_name"],
          //                           style: TextStyle(color: Colors.white),
          //                         ),
          //                       ],
          //                     ),
          //                   ),
          //                 );
          //               });
          //         } else {
          //           return SizedBox();
          //         }
          //       }),
          // ),
        ],
      ),
    );
  }
}
