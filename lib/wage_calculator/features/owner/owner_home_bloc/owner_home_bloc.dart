import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:equatable/equatable.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:get_storage/get_storage.dart';

part 'owner_home_event.dart';

part 'owner_home_state.dart';

class OwnerHomeBloc extends Bloc<OwnerHomeEvent, OwnerHomeState> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );
  final getStorage = GetStorage();

  OwnerHomeBloc() : super(OwnerHomeInitial()) {
    try {
      emit(OwnerHomeLoading());
      List<Map<String, dynamic>> items = [];
      on<LoadOwnerEvent>((event, emit) async {
        QuerySnapshot querySnapshot = await secondDb
            .collection('worker')
            .where('owner_id', isEqualTo: getStorage.read('id'))
            .where('work_type_id', isEqualTo: [event.workTypeId]).get();
        // print(querySnapshot.docs);
        for (QueryDocumentSnapshot doc in querySnapshot.docs) {
          Map<String, dynamic> map = {};
          Map<String, dynamic> data = doc.data() as Map<String, dynamic>;
          map['id'] = doc.id;
          map['fullname'] = "${data['worker_name']} ${data['worker_surname']}";

          DocumentSnapshot workName = await secondDb
              .collection('work_types')
              .doc(data['work_type_id'][0])
              .get();
          map['work_name'] =
              (workName.data() as Map<String, dynamic>)['work_name'];
          num totalSum = 0;
          QuerySnapshot querySnapshot1 = await secondDb
              .collection("worker")
              .doc(doc.id)
              .collection('calculation_list')
              .get();
          for (QueryDocumentSnapshot doc2 in querySnapshot1.docs) {
            totalSum += doc2['balance'];
            map['total_sum'] = totalSum;
          }
          items.add(map);
          print(items);
        }
        emit(OwnerHomeSuccess(items: items));
      });
    } catch (e) {
      emit(OwnerHomeError(message: e.toString()));
    }
  }
}
