part of 'owner_home_bloc.dart';

sealed class OwnerHomeState extends Equatable {
  const OwnerHomeState();
}

final class OwnerHomeInitial extends OwnerHomeState {
  @override
  List<Object> get props => [];
}

final class OwnerHomeLoading extends OwnerHomeState {
  @override
  List<Object> get props => [];
}

final class OwnerHomeSuccess extends OwnerHomeState {
  final List<Map<String, dynamic>> items;

  const OwnerHomeSuccess({required this.items});

  @override
  List<Object> get props => [items];
}

final class OwnerHomeError extends OwnerHomeState {
  final String message;

  const OwnerHomeError({required this.message});

  @override
  List<Object> get props => [message];
}
