import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:intl/intl.dart';

import '../../core/util/app_constants.dart';


class LoginSettings extends StatefulWidget {
  const LoginSettings({super.key});

  @override
  State<LoginSettings> createState() => _LoginSettingsState();
}

class _LoginSettingsState extends State<LoginSettings> {
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(app: Firebase.app(), databaseURL: 'wage-calculation');
  final getStorage = GetStorage();
  var formattedDate = DateFormat('dd.MM.yyyy HH:mm');
  final _formKey = GlobalKey<FormState>();
  TextEditingController login = TextEditingController();
  TextEditingController password = TextEditingController();
  bool isObscure = true;

  Future<DocumentSnapshot> getDocumentById(String documentId) async {
    try {
      DocumentSnapshot document = await secondDb
          .collection(getStorage.read("owner") ? 'owners' : "worker")
          .doc(documentId)
          .get();

      if (document.exists) {
        // Access data using document.data()
        Map<String, dynamic> data = document.data() as Map<String, dynamic>;
        print(data);
        return document;
      } else {
        throw Exception('Document does not exist');
      }
    } catch (e) {
      throw Exception('Error getting document: $e');
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
        appBar: AppBar(
          iconTheme: IconThemeData(color: Colors.white),
          backgroundColor: cAppBarColor,
          title: Text(
            "Login so'zlamalari",
            style: TextStyle(color: Colors.white),
          ),
        ),
        body: FutureBuilder(
            future: getDocumentById(getStorage.read("id")),
            builder: (context, asyncSnapshot) {
              if (asyncSnapshot.connectionState == ConnectionState.active ||
                  asyncSnapshot.connectionState == ConnectionState.done) {
                login =
                    TextEditingController(text: asyncSnapshot.data?['login']);
                password = TextEditingController(
                    text: asyncSnapshot.data?['password']);
                return Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Form(
                        key: _formKey,
                        child: Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: Column(

                            children: [
                              TextFormField(
                                style: TextStyle(color: Colors.white),
                                  controller: login,
                                  decoration: InputDecoration(
                                    labelText: "Login",
                                    labelStyle: TextStyle(color: Colors.white),
                                    border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(20)),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Ma`lumot kiritilishi kerak';
                                    }
                                    return null;
                                  }),
                              TextFormField(
                                  obscureText: isObscure,
                                  controller: password,
                                  style: TextStyle(color: Colors.white),
                                  decoration: InputDecoration(
                                    suffixIcon: InkWell(
                                      onTap: () {
                                        setState(() {
                                          isObscure = !isObscure;
                                        });
                                      },
                                      child: Icon(isObscure
                                          ? Icons.visibility
                                          : Icons.visibility_off,color: Colors.white,),
                                    ),
                                    labelText: "password",
                                    labelStyle: TextStyle(color: Colors.white),
                                    border: OutlineInputBorder(
                                        borderRadius:
                                            BorderRadius.circular(20)),
                                  ),
                                  validator: (value) {
                                    if (value == null || value.isEmpty) {
                                      return 'Ma`lumot kiritilishi kerak';
                                    }
                                    return null;
                                  }),
                              MaterialButton(
                                height: 40,
                                shape: RoundedRectangleBorder(
                                    borderRadius: BorderRadius.circular(20)),
                                minWidth: MediaQuery.of(context).size.width,
                                onPressed: () async {
                                  if (_formKey.currentState!.validate()) {
                                    secondDb
                                        .collection(getStorage.read("owner")
                                            ? 'owners'
                                            : "worker")
                                        .doc(getStorage.read("id"))
                                        .update({
                                      "login": login.text,
                                      "password": password.text
                                    }).then((value) {
                                      Navigator.pop(context);
                                    });
                                  }
                                },
                                color: cButtonColor,
                                child: Text(
                                  "Ok",
                                  style: TextStyle(color: Colors.white),
                                ),
                              ),
                            ],
                          ),
                        ))
                  ],
                );
              } else {
                return SizedBox();
              }
            }));
  }
}
