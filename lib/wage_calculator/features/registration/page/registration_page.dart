import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../../core/util/app_constants.dart';


class RegistrationPage extends StatefulWidget {
  const RegistrationPage({super.key});

  @override
  State<RegistrationPage> createState() => _RegistrationPageState();
}

class _RegistrationPageState extends State<RegistrationPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController login = TextEditingController();
  TextEditingController password = TextEditingController();
  TextEditingController name = TextEditingController();
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(),
      databaseURL: 'wage-calculation'
  );

  bool isObscure = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(backgroundColor: cAppBarColor,),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(

                  children: [
                    TextFormField(
                        controller: name,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: "Korxona nomi",
                          labelStyle: TextStyle(color: Colors.white),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Ma`lumot kiritilishi kerak';
                          }
                          return null;
                        }),
                    TextFormField(
                        controller: login,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelStyle: TextStyle(color: Colors.white),
                          labelText: "Login",
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Ma`lumot kiritilishi kerak';
                          }
                          return null;
                        }),
                    TextFormField(
                        controller: password,
                        obscureText: isObscure,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelStyle: TextStyle(color: Colors.white),
                          suffixIcon: InkWell(
                            onTap: () {
                              setState(() {
                                isObscure = !isObscure;
                              });
                            },
                            child: Icon(
                              isObscure
                                  ? Icons.visibility
                                  : Icons.visibility_off,
                              color: Colors.white,
                            ),
                          ),
                          labelText: "password",
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Ma`lumot kiritilishi kerak';
                          }
                          return null;
                        }),
                    MaterialButton(
                      height: 40,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20)),
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          QuerySnapshot querySnapshot = await secondDb
                              .collection('owners')
                              .where('login', isEqualTo: login.text)
                              .where('password', isEqualTo: password.text)
                              .get();
                          if (querySnapshot.docs.isNotEmpty) {
                            showTopSnackBar(
                              Overlay.of(context),
                              CustomSnackBar.error(
                                message: "Bunday foydalanuvchi mavjud!",
                              ),
                            );
                          } else {
                            secondDb.collection('owners').add({
                              "name": name.text,
                              "login": login.text,
                              "password": password.text,
                            }).then((DocumentReference doc) {
                              Navigator.pop(context);
                              print("Document added with ID: ${doc.id}");
                              // Operation successful
                            }).catchError((error) {
                              showTopSnackBar(
                                Overlay.of(context),
                                CustomSnackBar.error(
                                  message: "Error adding document: $error",
                                ),
                              );
                              // Handle error
                            });
                          }
                        }
                      },
                      child: Text(
                        "Ok",
                        style: TextStyle(color: Colors.white),
                      ),
                      color: cButtonColor,
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }
}
