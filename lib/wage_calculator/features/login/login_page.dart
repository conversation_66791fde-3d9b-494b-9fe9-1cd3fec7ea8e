import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:market/wage_calculator/features/owner/report_page.dart';
import 'package:market/wage_calculator/features/worker/worker_home_page.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import '../../../core/util/app_constants.dart';
import '../../../core/widgets/keyboard_dismissible_widget.dart';
import '../../password.dart';


class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController login = TextEditingController();
  TextEditingController password = TextEditingController();
  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(app: Firebase.app());
  bool isChecked = false;
  final getStorage = GetStorage();
  bool isObscure = true;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        backgroundColor: cAppBarColor,
      ),
      body: KeyboardDismissibleWidget(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Form(
                key: _formKey,
                child: Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(

                    children: [
                      TextFormField(
                          controller: login,
                          style: TextStyle(color: Colors.white),
                          decoration: InputDecoration(
                            labelText: "Login",
                            labelStyle: TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20)),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Ma`lumot kiritilishi kerak';
                            }
                            return null;
                          }),
                      TextFormField(
                          style: TextStyle(color: Colors.white),
                          controller: password,
                          obscureText: isObscure,
                          decoration: InputDecoration(
                            suffixIcon: InkWell(
                              onTap: () {
                                setState(() {
                                  isObscure = !isObscure;
                                });
                              },
                              child: Icon(
                                isObscure
                                    ? Icons.visibility
                                    : Icons.visibility_off,
                                color: Colors.white,
                              ),
                            ),
                            labelText: "password",
                            labelStyle: TextStyle(color: Colors.white),
                            border: OutlineInputBorder(
                                borderRadius: BorderRadius.circular(20)),
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'Ma`lumot kiritilishi kerak';
                            }
                            return null;
                          }),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.end,
                        children: [
                          SizedBox(
                            height: 30,
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Checkbox(
                                  value: isChecked,
                                  onChanged: (bool? value) {
                                    setState(() {
                                      isChecked = value!;
                                    });
                                  },
                                  activeColor: Colors.white,
                                  checkColor: Colors.black,
                                  focusColor: Colors.white, // kmark color
                                ),
                                SizedBox(
                                  width: 10,
                                ),
                                Text(
                                  "Owner sifatida",
                                  style: TextStyle(
                                      fontSize: 18, color: Colors.white),
                                ),
                              ],
                            ),
                          ),
                        ],
                      ),
                      MaterialButton(
                        height: 40,
                        shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(20)),
                        minWidth: MediaQuery.of(context).size.width,
                        onPressed: () async {
                          if (_formKey.currentState!.validate()) {
                            QuerySnapshot querySnapshot = await secondDb
                                .collection(isChecked ? 'owners' : 'worker')
                                .where('login', isEqualTo: login.text)
                                .where('password', isEqualTo: password.text)
                                .get();
                            if (querySnapshot.docs.isNotEmpty) {
                              Map<String, dynamic> data =
                                  querySnapshot.docs.first.data()
                                      as Map<String, dynamic>;
                              getStorage.write("owner", isChecked);
                              if (isChecked) {
                                getStorage.write(
                                    "id", querySnapshot.docs.first.id);
                                getStorage.write("name", data['name']);
                                Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) => ReportPage.screen()));
                              } else {
                                print(
                                    "-------->${(querySnapshot.docs.first.data() as Map)['work_type_id'][0]}");
                                getStorage.write(
                                    "id", querySnapshot.docs.first.id);
                                getStorage.write(
                                    "owner_id",
                                    (querySnapshot.docs.first.data()
                                        as Map)['owner_id']);
                                getStorage.write(
                                    "work_type_id",
                                    (querySnapshot.docs.first.data()
                                        as Map)['work_type_id'][0]);
                                getStorage.write("fullname",
                                    "${data['worker_name']} ${data['worker_surname']}");
                                Navigator.pushReplacement(
                                    context,
                                    MaterialPageRoute(
                                        builder: (context) =>
                                            WorkerHomePage()));
                              }
                            } else {
                              showTopSnackBar(
                                Overlay.of(context),
                                CustomSnackBar.error(
                                  message: "Foydalanuvchi topilmadi!",
                                ),
                              );
                            }
                          }
                        },
                        color: cButtonColor,
                        child: Text(
                          "Ok",
                          style: TextStyle(color: Colors.white),
                        ),
                      ),
                      TextButton(
                          onPressed: () {
                            Navigator.push(
                                context,
                                MaterialPageRoute(
                                    builder: (context) => PasswordPage()));
                          },
                          child: Text(
                            "Ro'yxatdan o'tish",
                            style: TextStyle(color: Colors.white),
                          ))
                    ],
                  ),
                ))
          ],
        ),
      ),
    );
  }
}
