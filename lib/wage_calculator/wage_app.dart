import 'package:flutter/material.dart';
import 'package:get_storage/get_storage.dart';
import 'package:market/wage_calculator/features/owner/report_page.dart';

import 'features/login/login_page.dart';
import 'features/worker/worker_home_page.dart';

class WageCalculatorAppProvider extends StatefulWidget {
  const WageCalculatorAppProvider({super.key});

  @override
  State<WageCalculatorAppProvider> createState() => _WageCalculatorAppProviderState();
}

class _WageCalculatorAppProviderState extends State<WageCalculatorAppProvider> {
  final getStorage = GetStorage();

  @override
  Widget build(BuildContext context) {
    // Consider moving this logic to a separate service/provider
    final userId = getStorage.read('id');
    final isOwner = getStorage.read('owner');

    return _buildHomeScreen(userId, isOwner);
  }

  Widget _buildHomeScreen(String? userId, bool? isOwner) {
    if (userId == null) return const LoginPage();
    return isOwner == true ?  ReportPage.screen() : const WorkerHomePage();
  }
}
