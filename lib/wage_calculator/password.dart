import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';
import '../core/util/app_constants.dart';
import 'features/registration/page/registration_page.dart';

class PasswordPage extends StatefulWidget {
  const PasswordPage({super.key});

  @override
  State<PasswordPage> createState() => _PasswordPageState();
}

class _PasswordPageState extends State<PasswordPage> {
  final _formKey = GlobalKey<FormState>();
  TextEditingController password = TextEditingController();

  static FirebaseFirestore get secondDb => FirebaseFirestore.instanceFor(
      app: Firebase.app(), databaseURL: 'wage-calculation');

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: AppBar(
        backgroundColor: cAppBarColor,
        title: Text("Parolni kiriting",style: TextStyle(color: Colors.white),),
      ),
      body: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Form(
              key: _formKey,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(

                  children: [
                    TextFormField(
                        controller: password,
                        style: TextStyle(color: Colors.white),
                        decoration: InputDecoration(
                          labelText: "Parolni kiriting",
                          labelStyle: TextStyle(color: Colors.white),
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(20)),
                        ),
                        validator: (value) {
                          if (value == null || value.isEmpty) {
                            return 'Ma`lumot kiritilishi kerak';
                          }
                          return null;
                        }),
                    MaterialButton(
                      height: 40,
                      shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(20)),
                      minWidth: MediaQuery.of(context).size.width,
                      onPressed: () async {
                        if (_formKey.currentState!.validate()) {
                          DocumentReference documentReference =
                              secondDb.collection('password').doc('password');
                          documentReference.get().then((value) {
                            String passwordFromBase = value['password'];
                            print(passwordFromBase);
                            String passwordText = password.text;
                            print(passwordText == passwordFromBase);
                            if (passwordText == passwordFromBase) {
                              Navigator.push(
                                  context,
                                  MaterialPageRoute(
                                      builder: (context) =>
                                          RegistrationPage()));
                            } else {
                              showTopSnackBar(
                                Overlay.of(context),
                                CustomSnackBar.error(
                                  message: "Parol noto'g'ri!",
                                ),
                              );
                            }
                          });
                        }
                      },
                      color: cButtonColor,
                      child: Text(
                        "Ok",
                        style: TextStyle(color: Colors.white),
                      ),
                    ),
                  ],
                ),
              ))
        ],
      ),
    );
  }
}
