import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/material.dart';
import 'package:badges/badges.dart' as badges;
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:hive_flutter/hive_flutter.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/app.dart';
import 'package:market/change_password_page.dart';
import 'package:market/check_page.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/model/product.dart';
import 'package:market/product_add_page.dart';
import 'package:market/product_update_page.dart';
import 'package:market/shopping_list_page.dart';
import 'package:market/user.dart';
import 'package:pattern_formatter/numeric_formatter.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'firebase_options.dart';
import 'wage_calculator/features/worker/worker_home_page.dart';

late Box<Product> productBox;

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  // Add this when initializing Firebase
  FirebaseFirestore.instance.settings = Settings(
      persistenceEnabled: true, cacheSizeBytes: Settings.CACHE_SIZE_UNLIMITED);
  await Hive.initFlutter();
  Hive.registerAdapter(ProductAdapter());
  productBox = await Hive.openBox("product");
  runApp(MaterialApp(
    home: const AppProvider(),
    debugShowCheckedModeBanner: false,
    theme: ThemeData(
      // Set the primary color scheme
      primarySwatch: Colors.blue,

      // Icon theme for all icons in the app
      iconTheme: const IconThemeData(
        color: Colors.white,
      ),

      // Primary icon theme (for app bars, etc.)
      primaryIconTheme: const IconThemeData(
        color: Colors.white,
      ),

      // App bar theme
      appBarTheme: const AppBarTheme(
        iconTheme: IconThemeData(
          color: Colors.white,
        ),
        actionsIconTheme: IconThemeData(
          color: Colors.white,
        ),
      ),

      // Bottom navigation bar theme
      bottomNavigationBarTheme: const BottomNavigationBarThemeData(
        selectedIconTheme: IconThemeData(
          color: Colors.white,
        ),
        unselectedIconTheme: IconThemeData(
          color: Colors.white,
        ),
      ),

      // Floating action button theme
      floatingActionButtonTheme: const FloatingActionButtonThemeData(
        foregroundColor: Colors.white,
      ),

      // List tile theme
      listTileTheme: const ListTileThemeData(
        iconColor: Colors.white,
      ),

      // Input decoration theme for text fields
      inputDecorationTheme: const InputDecorationTheme(
        prefixIconColor: Colors.white,
        suffixIconColor: Colors.white,
      ),

      // Card theme
      cardTheme: const CardTheme(
        color: cItemColor,
      ),

      // Dialog theme
      dialogTheme: const DialogTheme(
        iconColor: Colors.white,
      ),

      // Drawer theme
      drawerTheme: const DrawerThemeData(
        backgroundColor: cItemColor,
      ),

      // Navigation rail theme
      navigationRailTheme: const NavigationRailThemeData(
        selectedIconTheme: IconThemeData(
          color: Colors.white,
        ),
        unselectedIconTheme: IconThemeData(
          color: Colors.white,
        ),
      ),

      // Tab bar theme
      tabBarTheme: const TabBarTheme(
        labelColor: Colors.white,
        unselectedLabelColor: Colors.white,
      ),

      // Chip theme
      chipTheme: const ChipThemeData(
        deleteIconColor: Colors.white,
      ),

      // Expansion tile theme
      expansionTileTheme: const ExpansionTileThemeData(
        iconColor: Colors.white,
        collapsedIconColor: Colors.white,
      ),
    ),
  ));
}

class HomePage extends StatefulWidget {
  final String? saleId;

  const HomePage({super.key, this.saleId});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  final CollectionReference _product =
      FirebaseFirestore.instance.collection("products");

  int count = 0;
  TextEditingController searchController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController countController = TextEditingController(text: "1");
  bool hasSearchText = false;
  bool _isSearchFocused = false;
  bool _isFabExpanded = false;

  late AnimationController _fabAnimationController;
  late AnimationController _searchAnimationController;
  late Animation<double> _fabAnimation;
  late Animation<double> _searchAnimation;

  final FocusNode _searchFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    _fabAnimationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _searchAnimationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );

    _fabAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fabAnimationController, curve: Curves.easeInOut),
    );
    _searchAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(
          parent: _searchAnimationController, curve: Curves.easeInOut),
    );

    _searchFocusNode.addListener(() {
      setState(() {
        _isSearchFocused = _searchFocusNode.hasFocus;
      });
      if (_isSearchFocused) {
        _searchAnimationController.forward();
      } else {
        _searchAnimationController.reverse();
      }
    });
  }

  @override
  void dispose() {
    _fabAnimationController.dispose();
    _searchAnimationController.dispose();
    _searchFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: cBackgroundColor,
      appBar: _buildRegularAppBar(),
      body: Column(
        children: [
          _buildSearchSection(),
          Expanded(
            child: _buildProductList(),
          ),
        ],
      ),
      floatingActionButton: _buildModernFAB(),
    );
  }

  PreferredSizeWidget _buildRegularAppBar() {
    return AppBar(
      elevation: 0,
      backgroundColor: cAppBarColor,
      automaticallyImplyLeading: false,
      leading: widget.saleId != null
          ? Padding(
              padding: const EdgeInsets.all(8.0),
              child: GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: cItemColor.withOpacity(0.6),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: cButtonColor.withOpacity(0.3),
                      width: 1,
                    ),
                  ),
                  child: const Icon(
                    Icons.arrow_back_ios,
                    color: cWhite,
                    size: 20,
                  ),
                ),
              ),
            )
          : null,
      title: InkWell(
        onDoubleTap: () {
          Navigator.push(context,
              MaterialPageRoute(builder: (context) => ChangePasswordPage()));
        },
        child: Text(
          'Mahsulotlar boshqaruvi',
          style: TextStyle(
            color: cWhite,
            fontWeight: FontWeight.bold,
            fontSize: 20,
          ),
        ),
      ),
      centerTitle: true,
      actions: [
        if (widget.saleId == null)
          Padding(
            padding: const EdgeInsets.only(right: 16.0),
            child: _buildShoppingCartButton(),
          ),
      ],
      flexibleSpace: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cAppBarColor.withOpacity(0.9),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildShoppingCartButton() {
    return ValueListenableBuilder(
      valueListenable: productBox.listenable(),
      builder: (context, value, child) {
        return GestureDetector(
          onTap: () {
            Navigator.push(
              context,
              MaterialPageRoute(
                builder: (context) => ShoppingListPage(
                  productBox: productBox,
                  product: _product,
                ),
              ),
            );
          },
          child: Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(color: cButtonColor.withOpacity(0.3)),
            ),
            child: badges.Badge(
              badgeContent: Text(
                productBox.length.toString(),
                style: const TextStyle(
                  color: cWhite,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
              badgeStyle: badges.BadgeStyle(
                badgeColor: cRed,
                padding: const EdgeInsets.all(6),
              ),
              child: Icon(
                Icons.shopping_cart_outlined,
                color: cButtonColor,
                size: 24,
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildSearchSection() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 10),
      child: AnimatedBuilder(
        animation: _searchAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.8),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: _isSearchFocused ? cButtonColor : cGray.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: _isSearchFocused
                      ? cButtonColor.withOpacity(0.3)
                      : Colors.black.withOpacity(0.1),
                  blurRadius: _isSearchFocused ? 15 : 8,
                  offset: const Offset(0, 4),
                ),
              ],
            ),
            child: TextField(
              controller: searchController,
              focusNode: _searchFocusNode,
              style: const TextStyle(color: cWhite, fontSize: 16),
              decoration: InputDecoration(
                hintText: 'Mahsulotlarni qidiring...',
                hintStyle: TextStyle(color: cGray, fontSize: 16),
                prefixIcon: Icon(
                  Icons.search_rounded,
                  color: _isSearchFocused ? cButtonColor : cGray,
                  size: 24,
                ),
                suffixIcon: hasSearchText
                    ? GestureDetector(
                        onTap: () {
                          setState(() {
                            searchController.clear();
                            hasSearchText = false;
                          });
                        },
                        child: Icon(
                          Icons.clear_rounded,
                          color: cGray,
                          size: 20,
                        ),
                      )
                    : null,
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 16,
                ),
              ),
              onChanged: (value) {
                setState(() {
                  hasSearchText = value.isNotEmpty;
                });
              },
            ),
          );
        },
      ),
    );
  }

  Widget _buildProductList() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      child: StreamBuilder(
        stream: _product.orderBy("name").snapshots(),
        builder: (BuildContext context,
            AsyncSnapshot<QuerySnapshot<Object?>> snapshot) {
          if (snapshot.hasData) {
            final filteredDocs = snapshot.data!.docs.where((doc) {
              if (searchController.text.isEmpty) return true;
              return doc['name']
                  .toString()
                  .toLowerCase()
                  .contains(searchController.text.toLowerCase());
            }).toList();

            if (filteredDocs.isEmpty) {
              return _buildEmptyState();
            }

            return ListView.builder(
              itemCount: filteredDocs.length,
              itemBuilder: (BuildContext context, int index) {
                final DocumentSnapshot documentSnapshot = filteredDocs[index];
                return _buildModernProductCard(documentSnapshot);
              },
            );
          } else {
            return _buildLoadingState();
          }
        },
      ),
    );
  }

  Widget _buildModernProductCard(DocumentSnapshot documentSnapshot) {
    final bool isInStock = documentSnapshot['count'] > 0;
    final String productId = documentSnapshot.id;
    final String productName = documentSnapshot['name'];
    final int productPrice = documentSnapshot['price'];
    final int productCount = documentSnapshot['count'];

    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: Slidable(
        key: ValueKey(productId),
        endActionPane: ActionPane(
          motion: const StretchMotion(),
          children: [
            SlidableAction(
              onPressed: (BuildContext context) {
                _showDeleteDialog(productId);
              },
              backgroundColor: cRed,
              foregroundColor: cWhite,
              icon: Icons.delete_outline,
              label: "O'chirish",
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                bottomLeft: Radius.circular(16),
              ),
            ),
            SlidableAction(
              onPressed: (BuildContext context) {
                Navigator.push(
                  context,
                  MaterialPageRoute(
                    builder: (context) => ProductUpdatePage(
                      product: _product,
                      data: {
                        "name": productName,
                        "price": productPrice,
                        "id": productId,
                        "count": productCount,
                      },
                    ),
                  ),
                );
              },
              backgroundColor: cBlue,
              foregroundColor: cWhite,
              icon: Icons.edit_outlined,
              label: 'Tahrirlash',
              borderRadius: const BorderRadius.only(
                topRight: Radius.circular(16),
                bottomRight: Radius.circular(16),
              ),
            ),
          ],
        ),
        child: GestureDetector(
          onTap: () {
            if (widget.saleId != null) {
              showBottomSheetForUpdateSale(
                id: widget.saleId!,
                productId: productId,
                name: productName,
                price: productPrice,
                count: productCount,
              );
            } else {
              showBottomSheetForSale(
                productId,
                productName,
                productPrice,
                productCount,
              );
            }
          },
          child: Container(
            width: MediaQuery.of(context).size.width,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(20),
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: isInStock
                    ? [
                        cItemColor,
                        cItemColor.withOpacity(0.8),
                      ]
                    : [
                        cItemColor.withOpacity(0.6),
                        cItemColor.withOpacity(0.4),
                      ],
              ),
              boxShadow: [
                BoxShadow(
                  color: isInStock
                      ? cButtonColor.withOpacity(0.2)
                      : Colors.black.withOpacity(0.1),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
              border: Border.all(
                color: isInStock
                    ? cButtonColor.withOpacity(0.4)
                    : cGray.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            padding: const EdgeInsets.all(12),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Row(
                        children: [
                          Container(
                            padding: const EdgeInsets.all(8),
                            decoration: BoxDecoration(
                              color: isInStock
                                  ? cButtonColor.withOpacity(0.2)
                                  : cGray.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: isInStock
                                    ? cButtonColor.withOpacity(0.4)
                                    : cGray.withOpacity(0.4),
                                width: 1,
                              ),
                            ),
                            child: Icon(
                              isInStock
                                  ? Icons.inventory_2_outlined
                                  : Icons.inventory_outlined,
                              color: isInStock ? cButtonColor : cGray,
                              size: 20,
                            ),
                          ),
                          const SizedBox(width: 12),
                          Flexible(
                            child: Text(
                              productName,
                              style: const TextStyle(
                                color: cWhite,
                                fontSize: 18,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      decoration: BoxDecoration(
                        color: isInStock
                            ? Colors.green.withOpacity(0.15)
                            : cYellow.withOpacity(0.15),
                        borderRadius: BorderRadius.circular(25),
                        border: Border.all(
                          color: isInStock ? Colors.green : cYellow,
                          width: 1.5,
                        ),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isInStock
                                ? Icons.check_circle_outline
                                : Icons.schedule_outlined,
                            color: isInStock ? Colors.green : cYellow,
                            size: 14,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            isInStock ? 'Mavjud' : 'Tugagan',
                            style: TextStyle(
                              color: isInStock ? Colors.green : cYellow,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
                  decoration: BoxDecoration(
                    color: cGray.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Text(
                    'ID: $productId',
                    style: TextStyle(
                      color: cGray,
                      fontSize: 11,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
                SizedBox(
                  height: 8,
                ),
                Row(
                  children: [
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: cButtonColor.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: cButtonColor.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.payments_outlined,
                                  color: cButtonColor,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Narxi',
                                  style: TextStyle(
                                    color: cGray,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '${NumberFormat("#,###", "en_US").format(productPrice)} UZS',
                              style: TextStyle(
                                color: cButtonColor,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                    const SizedBox(width: 8),
                    Expanded(
                      child: Container(
                        padding: const EdgeInsets.all(10),
                        decoration: BoxDecoration(
                          color: isInStock
                              ? Colors.green.withOpacity(0.1)
                              : cGray.withOpacity(0.1),
                          borderRadius: BorderRadius.circular(16),
                          border: Border.all(
                            color: isInStock
                                ? Colors.green.withOpacity(0.3)
                                : cGray.withOpacity(0.3),
                            width: 1,
                          ),
                        ),
                        child: Column(
                          children: [
                            Row(
                              mainAxisAlignment: MainAxisAlignment.center,
                              children: [
                                Icon(
                                  Icons.inventory_2_outlined,
                                  color: isInStock ? Colors.green : cGray,
                                  size: 16,
                                ),
                                const SizedBox(width: 4),
                                Text(
                                  'Miqdori',
                                  style: TextStyle(
                                    color: cGray,
                                    fontSize: 12,
                                    fontWeight: FontWeight.w500,
                                  ),
                                ),
                              ],
                            ),
                            const SizedBox(height: 4),
                            Text(
                              '$productCount dona',
                              style: TextStyle(
                                color: isInStock ? Colors.green : cGray,
                                fontSize: 14,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(24),
            decoration: BoxDecoration(
              color: cGray.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.search_off_rounded,
              size: 60,
              color: cGray.withOpacity(0.6),
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Mahsulot topilmadi',
            style: TextStyle(
              color: cWhite,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Qidiruv so\'zlarini o\'zgartirib ko\'ring',
            style: TextStyle(
              color: cGray.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildLoadingState() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        children: [
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: CircularProgressIndicator(
              color: cButtonColor,
              strokeWidth: 3,
            ),
          ),
          const SizedBox(height: 24),
          Text(
            'Mahsulotlar yuklanmoqda...',
            style: TextStyle(
              color: cWhite,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  void _showDeleteDialog(String productId) {
    showDialog(
      context: context,
      builder: (context) {
        return AlertDialog(
          backgroundColor: cItemColor,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(20),
          ),
          title: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: cYellow.withOpacity(0.2),
                  shape: BoxShape.circle,
                ),
                child:
                    Icon(Icons.warning_amber_rounded, color: cYellow, size: 24),
              ),
              const SizedBox(width: 12),
              const Text(
                "Ogohlantirish",
                style: TextStyle(
                    color: cWhite, fontWeight: FontWeight.bold, fontSize: 18),
              ),
            ],
          ),
          content: const Text(
            "Ushbu mahsulotni o'chirishni xohlaysizmi? Bu amalni bekor qilib bo'lmaydi.",
            style: TextStyle(color: cWhite, fontSize: 16),
          ),
          actions: [
            TextButton(
              onPressed: () => Navigator.pop(context),
              child: Container(
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 10),
                decoration: BoxDecoration(
                  color: cGray.withOpacity(0.2),
                  borderRadius: BorderRadius.circular(10),
                ),
                child: Text(
                  "Bekor qilish",
                  style: TextStyle(color: cGray, fontWeight: FontWeight.w600),
                ),
              ),
            ),
            ElevatedButton(
              onPressed: () async {
                bool hasInternet =
                    await InternetConnectionChecker().hasConnection;
                if (hasInternet) {
                  try {
                    await _product.doc(productId).delete();
                    Navigator.pop(context);
                    showTopSnackBar(
                      Overlay.of(context),
                      CustomSnackBar.success(
                        message: "Mahsulot muvaffaqiyatli o'chirildi!",
                      ),
                    );
                  } catch (e) {
                    Navigator.pop(context);
                    showTopSnackBar(
                      Overlay.of(context),
                      CustomSnackBar.error(
                        message: "Mahsulotni o'chirishda xatolik yuz berdi!",
                      ),
                    );
                  }
                } else {
                  Navigator.pop(context);
                  showTopSnackBar(
                    Overlay.of(context),
                    CustomSnackBar.error(
                      message:
                          "Internet aloqasi yo'q! Qaytadan urinib ko'ring.",
                    ),
                  );
                }
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: cRed,
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(10),
                ),
                padding:
                    const EdgeInsets.symmetric(horizontal: 20, vertical: 12),
              ),
              child: const Text(
                "O'chirish",
                style: TextStyle(color: cWhite, fontWeight: FontWeight.w600),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildModernFAB() {
    return AnimatedBuilder(
      animation: _fabAnimation,
      builder: (context, child) {
        return Column(
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            if (_isFabExpanded) ...[
              _buildFABItem(
                icon: Icons.person_outline,
                label: 'Klientlar',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => UserManagementPage()),
                  );
                  _toggleFAB();
                },
              ),
              // const SizedBox(height: 12),
              // _buildFABItem(
              //   icon: Icons.work_outline,
              //   label: 'Nasiya Daftar',
              //   onTap: () {
              //     Navigator.push(
              //       context,
              //       MaterialPageRoute(builder: (_) => WorkerHomePage()),
              //     );
              //     _toggleFAB();
              //   },
              // ),
              const SizedBox(height: 12),
              _buildFABItem(
                icon: Icons.newspaper_outlined,
                label: 'Savdo bo\'limi',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(builder: (_) => CheckPage()),
                  );
                  _toggleFAB();
                },
              ),
              const SizedBox(height: 12),
              _buildFABItem(
                icon: Icons.add_box_outlined,
                label: 'Mahsulot qo\'shish',
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (_) => ProductAddPage(product: _product),
                    ),
                  );
                  _toggleFAB();
                },
              ),
              const SizedBox(height: 16),
            ],
            GestureDetector(
              onTap: _toggleFAB,
              child: Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      cButtonColor,
                      cButtonColor.withOpacity(0.8),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(30),
                  boxShadow: [
                    BoxShadow(
                      color: cButtonColor.withOpacity(0.4),
                      blurRadius: 15,
                      offset: const Offset(0, 6),
                    ),
                  ],
                ),
                child: AnimatedRotation(
                  turns: _isFabExpanded ? 0.125 : 0,
                  duration: const Duration(milliseconds: 300),
                  child: const Icon(
                    Icons.add,
                    color: cWhite,
                    size: 28,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildFABItem({
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: cItemColor,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(color: cButtonColor.withOpacity(0.3)),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withOpacity(0.2),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: cButtonColor, size: 20),
            const SizedBox(width: 8),
            Text(
              label,
              style: const TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _toggleFAB() {
    setState(() {
      _isFabExpanded = !_isFabExpanded;
    });
    if (_isFabExpanded) {
      _fabAnimationController.forward();
    } else {
      _fabAnimationController.reverse();
    }
  }

  showBottomSheetForSale(String id, String name, int price, int count) {
    priceController = TextEditingController(
        text: NumberFormat("#,###", "en_US").format(price));
    countController = TextEditingController(text: "1");

    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return Container(
            decoration: const BoxDecoration(
              color: cBackgroundColor,
              borderRadius: BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            padding: EdgeInsets.only(
              bottom: MediaQuery.of(context).viewInsets.bottom + 20,
              left: 24,
              top: 20,
              right: 24,
            ),
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Handle bar
                  Center(
                    child: Container(
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: cGray.withOpacity(0.5),
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),
                  ),
                  const SizedBox(height: 20),

                  // Product info header
                  Container(
                    padding: const EdgeInsets.all(16),
                    decoration: BoxDecoration(
                      color: cItemColor.withOpacity(0.8),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(color: cButtonColor.withOpacity(0.3)),
                    ),
                    child: Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: cButtonColor.withOpacity(0.2),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Icon(
                            Icons.shopping_cart_outlined,
                            color: cButtonColor,
                            size: 24,
                          ),
                        ),
                        const SizedBox(width: 16),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                name,
                                style: const TextStyle(
                                  fontSize: 18,
                                  color: cWhite,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                              const SizedBox(height: 4),
                              Text(
                                count > 0
                                    ? 'Mavjud: $count dona'
                                    : 'Tugagan - lekin sotish mumkin',
                                style: TextStyle(
                                  fontSize: 14,
                                  color: count > 0 ? cGray : cYellow,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ],
                    ),
                  ),
                  const SizedBox(height: 24),
                  // Price input
                  Text(
                    'Narxi',
                    style: TextStyle(
                      fontSize: 16,
                      color: cWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: cItemColor.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: cButtonColor.withOpacity(0.3)),
                    ),
                    child: TextField(
                      controller: priceController,
                      keyboardType: TextInputType.number,
                      style: const TextStyle(color: cWhite, fontSize: 16),
                      inputFormatters: [ThousandsFormatter()],
                      decoration: InputDecoration(
                        hintText: 'Narxni kiriting',
                        hintStyle: TextStyle(color: cGray),
                        suffixIcon: Container(
                          padding: const EdgeInsets.all(12),
                          child: Text(
                            "UZS",
                            style: TextStyle(
                              color: cButtonColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        border: InputBorder.none,
                        contentPadding: const EdgeInsets.all(16),
                      ),
                    ),
                  ),
                  const SizedBox(height: 24),

                  // Quantity selector
                  Text(
                    'Miqdori',
                    style: TextStyle(
                      fontSize: 16,
                      color: cWhite,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    decoration: BoxDecoration(
                      color: cItemColor.withOpacity(0.6),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: cButtonColor.withOpacity(0.3)),
                    ),
                    padding:
                        const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                    child: Row(
                      children: [
                        GestureDetector(
                          onTap: () {
                            int currentValue =
                                int.tryParse(countController.text) ?? 1;
                            if (currentValue > 1) {
                              state(() {
                                countController.text =
                                    (currentValue - 1).toString();
                              });
                            }
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: cButtonColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: cButtonColor.withOpacity(0.3)),
                            ),
                            child: Icon(
                              Icons.remove,
                              color: cButtonColor,
                              size: 20,
                            ),
                          ),
                        ),
                        Expanded(
                          child: TextField(
                            controller: countController,
                            textAlign: TextAlign.center,
                            style: const TextStyle(
                              color: cWhite,
                              fontSize: 18,
                              fontWeight: FontWeight.w600,
                            ),
                            decoration: InputDecoration(
                              hintText: '1',
                              hintStyle: TextStyle(color: cGray),
                              border: InputBorder.none,
                              contentPadding:
                                  const EdgeInsets.symmetric(vertical: 12),
                            ),
                            keyboardType: TextInputType.number,
                            onChanged: (value) {
                              // Ensure minimum value is 1, allow any positive number
                              if (value.isEmpty ||
                                  int.tryParse(value) == null ||
                                  int.parse(value) < 1) {
                                state(() {
                                  countController.text = '';
                                });
                              }
                            },
                          ),
                        ),
                        GestureDetector(
                          onTap: () {
                            int currentValue =
                                int.tryParse(countController.text) ?? 1;
                            // Allow adding any quantity, even if out of stock
                            state(() {
                              countController.text =
                                  (currentValue + 1).toString();
                            });
                          },
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              color: cButtonColor.withOpacity(0.2),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                  color: cButtonColor.withOpacity(0.3)),
                            ),
                            child: Icon(
                              Icons.add,
                              color: cButtonColor,
                              size: 20,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Info message for out-of-stock items
                  if (count <= 0)
                    Container(
                      margin: const EdgeInsets.only(top: 16),
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        color: cYellow.withOpacity(0.1),
                        borderRadius: BorderRadius.circular(12),
                        border: Border.all(color: cYellow.withOpacity(0.3)),
                      ),
                      child: Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            color: cYellow,
                            size: 20,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'Bu mahsulot tugagan, lekin sotish mumkin. Ombor miqdori 0 dan kam bo\'lmaydi.',
                              style: TextStyle(
                                color: cYellow,
                                fontSize: 13,
                                fontWeight: FontWeight.w500,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                  const SizedBox(height: 32),

                  // Action buttons
                  Row(
                    children: [
                      Expanded(
                        child: GestureDetector(
                          onTap: () => Navigator.pop(context),
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            decoration: BoxDecoration(
                              color: Colors.red.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(color: cGray.withOpacity(0.3)),
                            ),
                            child: Center(
                              child: Icon(
                                Icons.close,
                                color: Colors.red,
                              ),
                            ),
                          ),
                        ),
                      ),
                      const SizedBox(width: 16),
                      Expanded(
                        flex: 2,
                        child: GestureDetector(
                          onTap: () {
                            int quantity =
                                int.tryParse(countController.text) ?? 1;
                            if (quantity > 0) {
                              // Check if product already exists in Hive
                              final products = productBox.values.toList();
                              final existingProductIndex =
                                  products.indexWhere((p) => p.productId == id);

                              if (existingProductIndex != -1) {
                                // Product exists, update its count
                                final existingProduct =
                                    products[existingProductIndex];
                                final updatedProduct = Product(
                                  name: existingProduct.name,
                                  price: existingProduct.price,
                                  count: existingProduct.count + quantity,
                                  id: existingProduct.id,
                                  productId: existingProduct.productId,
                                );
                                productBox.putAt(
                                    existingProductIndex, updatedProduct);
                              } else {
                                // Product doesn't exist, add new product
                                productBox.add(Product(
                                  name: name,
                                  price: int.parse(
                                      priceController.text.replaceAll(',', '')),
                                  count: quantity,
                                  id: id,
                                  productId: id,
                                ));
                              }
                            }
                            Navigator.pop(context);
                          },
                          child: Container(
                            padding: const EdgeInsets.symmetric(vertical: 16),
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: [
                                  cButtonColor,
                                  cButtonColor.withOpacity(0.8),
                                ],
                              ),
                              borderRadius: BorderRadius.circular(12),
                              boxShadow: [
                                BoxShadow(
                                  color: cButtonColor.withOpacity(0.3),
                                  blurRadius: 8,
                                  offset: const Offset(0, 4),
                                ),
                              ],
                            ),
                            child: Center(
                              child: Row(
                                mainAxisAlignment: MainAxisAlignment.center,
                                children: [
                                  Icon(
                                    Icons.shopping_cart_outlined,
                                    color: cWhite,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  const Text(
                                    'Savatga qo\'shish',
                                    style: TextStyle(
                                      color: cWhite,
                                      fontSize: 16,
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 20),
                ],
              ),
            ),
          );
        });
      },
    );
  }

  showBottomSheetForUpdateSale({
    required String id,
    required String productId,
    required String name,
    required int price,
    required int count,
  }) async {
    TextEditingController priceController = TextEditingController(
        text: NumberFormat("#,###", "en_US").format(price));

    // Check if this product already exists in the current sale
    int existingSaleCount = await _getExistingProductCountInSale(id, productId);

    // If product exists in sale, use existing count; otherwise start with 1
    TextEditingController countController = TextEditingController(
        text: existingSaleCount > 0 ? existingSaleCount.toString() : "1");
    showModalBottomSheet(
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      context: context,
      builder: (BuildContext context) {
        return StatefulBuilder(builder: (context, state) {
          return Container(
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cBackgroundColor,
                  cBackgroundColor.withOpacity(0.9),
                ],
              ),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(24),
                topRight: Radius.circular(24),
              ),
            ),
            child: SingleChildScrollView(
              child: Container(
                padding: EdgeInsets.only(
                  bottom: MediaQuery.of(context).viewInsets.bottom + 20,
                  left: 24,
                  top: 20,
                  right: 24,
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    // Handle bar
                    Center(
                      child: Container(
                        width: 50,
                        height: 4,
                        decoration: BoxDecoration(
                          color: cGray.withOpacity(0.5),
                          borderRadius: BorderRadius.circular(2),
                        ),
                      ),
                    ),
                    const SizedBox(height: 20),

                    // Header section
                    _buildUpdateHeader(name, existingSaleCount > 0),
                    const SizedBox(height: 24),
                    // Price section
                    _buildUpdatePriceField(state, priceController),
                    const SizedBox(height: 20),
                    // Quantity section
                    _buildUpdateQuantitySection(state, countController),
                    const SizedBox(height: 32),

                    // Action buttons
                    _buildUpdateActionButtons(
                        context,
                        id,
                        productId,
                        name,
                        priceController,
                        countController,
                        existingSaleCount > 0),
                  ],
                ),
              ),
            ),
          );
        });
      },
    );
  }

  Widget _buildUpdateHeader(String name, bool isEditing) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            cItemColor,
            cItemColor.withOpacity(0.8),
          ],
        ),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              borderRadius: BorderRadius.circular(10),
              border: Border.all(
                color: cButtonColor.withOpacity(0.4),
                width: 1,
              ),
            ),
            child: Icon(
              isEditing
                  ? Icons.edit_outlined
                  : Icons.add_shopping_cart_outlined,
              color: cButtonColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isEditing
                      ? 'Mahsulotni tahrirlash'
                      : 'Savdoga mahsulot qo\'shish',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  name,
                  style: const TextStyle(
                    fontSize: 18,
                    color: cWhite,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUpdatePriceField(
      StateSetter state, TextEditingController priceController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.payments_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot narxi',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: cButtonColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: priceController,
            keyboardType: TextInputType.number,
            style: const TextStyle(color: cWhite),
            inputFormatters: [ThousandsFormatter()],
            decoration: InputDecoration(
              hintText: 'Narxni kiriting',
              hintStyle: TextStyle(color: cGray),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
              suffixText: 'UZS',
              suffixStyle: TextStyle(
                color: cGray,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateQuantitySection(
      StateSetter state, TextEditingController countController) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              Icons.inventory_2_outlined,
              color: cButtonColor,
              size: 16,
            ),
            const SizedBox(width: 8),
            Text(
              'Mahsulot miqdori',
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: cButtonColor.withOpacity(0.3)),
          ),
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          child: Row(
            children: [
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  if (currentValue > 1) {
                    state(() {
                      countController.text = (currentValue - 1).toString();
                    });
                  }
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.remove,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
              Expanded(
                child: TextField(
                  controller: countController,
                  textAlign: TextAlign.center,
                  style: const TextStyle(
                    color: cWhite,
                    fontSize: 18,
                    fontWeight: FontWeight.w600,
                  ),
                  decoration: InputDecoration(
                    hintText: '1',
                    hintStyle: TextStyle(color: cGray),
                    border: InputBorder.none,
                    contentPadding: const EdgeInsets.symmetric(vertical: 12),
                    suffixText: 'dona',
                    suffixStyle: TextStyle(
                      color: cGray,
                      fontSize: 12,
                    ),
                  ),
                  keyboardType: TextInputType.number,
                  onChanged: (value) {
                    if (value.isEmpty ||
                        int.tryParse(value) == null ||
                        int.parse(value) < 1) {
                      state(() {
                        countController.text = '';
                      });
                    }
                  },
                ),
              ),
              GestureDetector(
                onTap: () {
                  int currentValue = int.tryParse(countController.text) ?? 1;
                  state(() {
                    countController.text = (currentValue + 1).toString();
                  });
                },
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: BoxDecoration(
                    color: cButtonColor.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: cButtonColor.withOpacity(0.3)),
                  ),
                  child: Icon(
                    Icons.add,
                    color: cButtonColor,
                    size: 20,
                  ),
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildUpdateActionButtons(
      BuildContext context,
      String id,
      String productId,
      String name,
      TextEditingController priceController,
      TextEditingController countController,
      bool isEditing) {
    return Row(
      children: [
        Expanded(
          child: GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                color: Colors.red.withOpacity(0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: cGray.withOpacity(0.3)),
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(Icons.close, color: Colors.red, size: 20),
                  ],
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 16),
        Expanded(
          flex: 2,
          child: GestureDetector(
            onTap: () async {
              try {
                int parsedPrice =
                    int.tryParse(priceController.text.replaceAll(',', '')) ?? 0;
                int parsedCount = int.tryParse(countController.text) ?? 1;

                if (parsedPrice <= 0) {
                  showTopSnackBar(
                    Overlay.of(context),
                    CustomSnackBar.error(
                      message: "Narx 0 dan katta bo'lishi kerak!",
                    ),
                  );
                  return;
                }

                if (parsedCount <= 0) {
                  showTopSnackBar(
                    Overlay.of(context),
                    CustomSnackBar.error(
                      message: "Miqdor 0 dan katta bo'lishi kerak!",
                    ),
                  );
                  return;
                }

                addOrUpdateProduct(
                    id: id,
                    productId: productId,
                    name: name,
                    price: parsedPrice,
                    count: parsedCount,
                    isUpdate: isEditing);
                Navigator.pop(context);
              } catch (e) {
                showTopSnackBar(
                  Overlay.of(context),
                  CustomSnackBar.error(
                    message: "Xatolik yuz berdi: $e",
                  ),
                );
              }
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16),
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.topLeft,
                  end: Alignment.bottomRight,
                  colors: [
                    cButtonColor,
                    cButtonColor.withOpacity(0.8),
                  ],
                ),
                borderRadius: BorderRadius.circular(12),
                boxShadow: [
                  BoxShadow(
                    color: cButtonColor.withOpacity(0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 4),
                  ),
                ],
              ),
              child: Center(
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    Icon(isEditing ? Icons.check : Icons.add_shopping_cart,
                        color: cBlack, size: 20),
                    const SizedBox(width: 8),
                    Text(
                      isEditing ? 'O\'zgartirish' : 'Savdoga qo\'shish',
                      style: TextStyle(
                        color: cBlack,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  // Helper method to check if product exists in current sale and get its count
  Future<int> _getExistingProductCountInSale(
      String saleId, String productId) async {
    try {
      final CollectionReference tradeHistory =
          FirebaseFirestore.instance.collection("trade_history");
      QuerySnapshot querySnapshot = await tradeHistory
          .doc(saleId)
          .collection('actions')
          .where('product_id', isEqualTo: productId)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        // Product exists in sale, return its current count
        return querySnapshot.docs.first['count'] ?? 0;
      } else {
        // Product doesn't exist in sale
        return 0;
      }
    } catch (e) {
      print('Error checking existing product in sale: $e');
      return 0;
    }
  }

  void addOrUpdateProduct(
      {required String id,
      required String productId,
      required String name,
      required int price,
      required int count,
      bool isUpdate = false}) async {
    bool result = await InternetConnectionChecker().hasConnection;
    final CollectionReference tradeHistory =
        FirebaseFirestore.instance.collection("trade_history");
    if (result == true) {
      // First, check if the product exists in the collection
      QuerySnapshot existingProducts = await tradeHistory
          .doc(id)
          .collection('actions')
          .where('product_id', isEqualTo: productId)
          .get();

      if (existingProducts.docs.isNotEmpty) {
        // Product exists
        DocumentSnapshot existingProduct = existingProducts.docs.first;
        int finalCount;

        if (isUpdate) {
          // If this is an update (editing existing product), replace the count
          finalCount = count;
        } else {
          // If this is adding more of the same product, add to existing count
          int currentCount = existingProduct['count'];
          finalCount = currentCount + count;
        }

        await tradeHistory
            .doc(id)
            .collection('actions')
            .doc(existingProduct.id)
            .update({"count": finalCount, "price": price});

        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.success(
            message: isUpdate
                ? "Mahsulot yangilandi!"
                : "Mahsulot miqdori qo'shildi!",
          ),
        );
      } else {
        // Product doesn't exist, add it
        await tradeHistory.doc(id).collection('actions').add({
          "name": name,
          "price": price,
          "count": count,
          "product_id": productId,
        });
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.success(
            message: "Yangi mahsulot qo'shildi!",
          ),
        );
      }
    } else {
      showTopSnackBar(
        Overlay.of(context),
        CustomSnackBar.error(
          message: "Internet aloqasi yo'q!",
        ),
      );
    }
  }
}
