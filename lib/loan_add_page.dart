import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:fluttertoast/fluttertoast.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/function/function.dart';
import 'package:market/load_page.dart';
import 'package:pattern_formatter/numeric_formatter.dart';

class LoanAddPage extends StatefulWidget {
  final DocumentSnapshot? documentSnapshot;

  const LoanAddPage({super.key, this.documentSnapshot});

  @override
  State<LoanAddPage> createState() => _LoanAddPageState();
}

class _LoanAddPageState extends State<LoanAddPage> {
  final CollectionReference loan =
      FirebaseFirestore.instance.collection("loan");

  TextEditingController txtNameController = TextEditingController();
  TextEditingController txtLoanController = TextEditingController();
  final DateFormat formatterDate = DateFormat('yyyy-MM-dd');


  @override
  void initState() {
    super.initState();
    if (widget.documentSnapshot != null) {
      List<dynamic> names = widget.documentSnapshot!['name'];
      txtNameController = TextEditingController(text:capitalFirst(names.last));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
          bottom: MediaQuery.of(context).viewInsets.bottom,
          right: 10,
          left: 10,
          top: 20),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              enabled: widget.documentSnapshot != null ? false : true,
              controller: txtNameController,
              decoration: const InputDecoration(
                  border: OutlineInputBorder(), hintText: "ism"),
            ),
            SizedBox(
              height: 10,
            ),
            TextField(
              controller: txtLoanController,
              keyboardType: TextInputType.number,
              inputFormatters: [ThousandsFormatter()],
              decoration: const InputDecoration(
                  hintText: "summa",
                  border: OutlineInputBorder(),
                  suffixIcon: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text("So'm"),
                    ],
                  )),
            ),
            SizedBox(
              height: 20,
            ),
            Visibility(
              visible: widget.documentSnapshot == null ? false : true,
              child: Row(
                children: [
                  Expanded(
                      child: MaterialButton(
                    onPressed: () async {
                      bool result =
                          await InternetConnectionChecker().hasConnection;
                      if (result == true) {
                        try {
                          int loanSumma = widget.documentSnapshot!['loan'];
                          loanSumma = loanSumma +
                              int.parse(
                                  txtLoanController.text.replaceAll(",", ""));
                          loan.doc(widget.documentSnapshot!.id)
                              .update({"loan": loanSumma});
                          Navigator.pop(context);
                        } catch (e) {
                          showToast("xatolik sodir bo'ldi");
                        }
                      } else {
                        showToast("Internet yo'q");
                      }
                    },
                    child: Text(
                      "Qarzga qoshish",
                      style: TextStyle(color: Colors.white),
                    ),
                    color: Colors.red,
                    height: 50,
                  )),
                  SizedBox(
                    width: 2,
                  ),
                  Expanded(
                      child: MaterialButton(
                    onPressed: () async {
                      DateTime now=DateTime.now();
                      bool result =
                          await InternetConnectionChecker().hasConnection;
                      if (result == true) {
                        try {
                          int loanSumma = widget.documentSnapshot!['loan'];
                          loanSumma = loanSumma -
                              int.parse(
                                  txtLoanController.text.replaceAll(",", ""));
                          loan.doc(widget.documentSnapshot!.id)
                              .update({"loan": loanSumma,"date":formatterDate.format(now)});
                          Navigator.pop(context);
                        } catch (e) {
                          showToast("xatolik sodir bo'ldi");
                        }
                      } else {
                        showToast("Internet yo'q");
                      }
                    },
                    child: Text("Qarzdan ayirish",
                        style: TextStyle(color: Colors.white)),
                    color: Colors.green,
                    height: 50,
                  )),
                ],
              ),
            ),
            Visibility(
              visible: widget.documentSnapshot == null ? true : false,
              child: MaterialButton(
                onPressed: () async {
                  DateTime now=DateTime.now();
                  bool result = await InternetConnectionChecker().hasConnection;
                  if (result == true) {
                    try {
                      if (txtLoanController.text.isNotEmpty &&
                          txtNameController.text.isNotEmpty) {
                        loan.add({
                          "name": setSearchParam(txtNameController.text.toLowerCase()),
                          "loan": int.parse(
                              txtLoanController.text.replaceAll(",", ""),
                          ),
                          "date":formatterDate.format(now)
                        });
                        Navigator.pop(context);
                      }
                    } catch (e) {
                      showToast("xatolik sodir bo'ldi");
                    }
                  } else {
                    showToast("Internet yo'q");
                  }
                },
                child: Text(
                  "Qarzga berish",
                  style: TextStyle(color: Colors.white),
                ),
                color: Colors.blue,
                minWidth: MediaQuery.of(context).size.width,
              ),
            ),
            SizedBox(
              height: 2,
            ),
          ],
        ),
      ),
    );
  }

  setSearchParam(String caseNumber) {
    List<String> caseSearchList = [];
    String temp = "";
    for (int i = 0; i < caseNumber.length; i++) {
      temp = temp + caseNumber[i];
      caseSearchList.add(temp);
    }
    return caseSearchList;
  }
}
