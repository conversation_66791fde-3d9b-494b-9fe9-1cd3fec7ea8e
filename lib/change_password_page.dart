import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get_storage/get_storage.dart';
import 'package:top_snackbar_flutter/custom_snack_bar.dart';
import 'package:top_snackbar_flutter/top_snack_bar.dart';

import 'core/util/app_constants.dart';

class ChangePasswordPage extends StatefulWidget {
  const ChangePasswordPage({super.key});

  @override
  State<ChangePasswordPage> createState() => _ChangePasswordPageState();
}

class _ChangePasswordPageState extends State<ChangePasswordPage>
    with TickerProviderStateMixin {
  final CollectionReference password =
      FirebaseFirestore.instance.collection("password");

  String _currentPassword = '';
  String _newPassword = '';
  String _confirmPassword = '';
  bool _isLoading = false;
  bool _hasError = false;
  int _currentStep = 0; // 0: current, 1: new, 2: confirm
  String _errorMessage = '';

  late AnimationController _shakeController;
  late AnimationController _fadeController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(begin: 0, end: 10).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onNumberPressed(String number) {
    if (_isLoading) return;

    setState(() {
      switch (_currentStep) {
        case 0:
          if (_currentPassword.length < 4) {
            _currentPassword += number;
          }
          break;
        case 1:
          if (_newPassword.length < 4) {
            _newPassword += number;
          }
          break;
        case 2:
          if (_confirmPassword.length < 4) {
            _confirmPassword += number;
          }
          break;
      }
      _hasError = false;
      _errorMessage = '';
    });

    HapticFeedback.lightImpact();

    // Auto-proceed when 4 digits are entered
    if (_getCurrentPassword().length == 4) {
      _handleStepCompletion();
    }
  }

  void _onBackspacePressed() {
    if (_isLoading) return;

    setState(() {
      switch (_currentStep) {
        case 0:
          if (_currentPassword.isNotEmpty) {
            _currentPassword = _currentPassword.substring(0, _currentPassword.length - 1);
          }
          break;
        case 1:
          if (_newPassword.isNotEmpty) {
            _newPassword = _newPassword.substring(0, _newPassword.length - 1);
          }
          break;
        case 2:
          if (_confirmPassword.isNotEmpty) {
            _confirmPassword = _confirmPassword.substring(0, _confirmPassword.length - 1);
          }
          break;
      }
      _hasError = false;
      _errorMessage = '';
    });
    HapticFeedback.lightImpact();
  }

  void _onClearPressed() {
    if (_isLoading) return;

    setState(() {
      switch (_currentStep) {
        case 0:
          _currentPassword = '';
          break;
        case 1:
          _newPassword = '';
          break;
        case 2:
          _confirmPassword = '';
          break;
      }
      _hasError = false;
      _errorMessage = '';
    });
    HapticFeedback.mediumImpact();
  }

  String _getCurrentPassword() {
    switch (_currentStep) {
      case 0:
        return _currentPassword;
      case 1:
        return _newPassword;
      case 2:
        return _confirmPassword;
      default:
        return '';
    }
  }

  void _handleStepCompletion() async {
    switch (_currentStep) {
      case 0:
        await _verifyCurrentPassword();
        break;
      case 1:
        _proceedToConfirmStep();
        break;
      case 2:
        await _confirmAndSavePassword();
        break;
    }
  }

  Future<void> _verifyCurrentPassword() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final snapshot = await password.get();
      if (snapshot.docs.isNotEmpty) {
        final DocumentSnapshot documentSnapshot = snapshot.docs[0];
        if (documentSnapshot['password'] == _currentPassword) {
          setState(() {
            _currentStep = 1;
            _isLoading = false;
          });
          HapticFeedback.heavyImpact();
        } else {
          _showError('Joriy parol noto\'g\'ri!');
        }
      } else {
        _showError('Parol topilmadi!');
      }
    } catch (e) {
      _showError('Xatolik yuz berdi!');
    }
  }

  void _proceedToConfirmStep() {
    if (_newPassword.length == 4) {
      if (_newPassword == _currentPassword) {
        _showError('Yangi parol eskisidan farq qilishi kerak!');
        return;
      }
      setState(() {
        _currentStep = 2;
      });
      HapticFeedback.heavyImpact();
    }
  }

  Future<void> _confirmAndSavePassword() async {
    if (_newPassword != _confirmPassword) {
      _showError('Parollar mos kelmaydi!');
      return;
    }

    setState(() {
      _isLoading = true;
    });

    try {
      final snapshot = await password.get();
      if (snapshot.docs.isNotEmpty) {
        final DocumentSnapshot documentSnapshot = snapshot.docs[0];
        await password.doc(documentSnapshot.id).update({
          'password': _newPassword,
        });

        // Save to local storage for backup
        final storage = GetStorage();
        await storage.write('backup_password', _newPassword);

        HapticFeedback.heavyImpact();
        showTopSnackBar(
          Overlay.of(context),
          CustomSnackBar.success(
            message: "Parol muvaffaqiyatli o'zgartirildi!",
          ),
        );

        Navigator.pop(context);
      }
    } catch (e) {
      _showError('Parolni saqlashda xatolik!');
    }
  }

  void _showError(String message) {
    setState(() {
      _hasError = true;
      _errorMessage = message;
      _isLoading = false;
      
      // Clear current step password
      switch (_currentStep) {
        case 0:
          _currentPassword = '';
          break;
        case 1:
          _newPassword = '';
          break;
        case 2:
          _confirmPassword = '';
          break;
      }
    });
    
    HapticFeedback.heavyImpact();
    _shakeController.forward().then((_) {
      _shakeController.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                _buildHeader(),
                _buildPasswordDisplay(),
                Expanded(
                  flex: 3,
                  child: _buildCustomKeypad(),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    List<String> titles = [
      'Joriy Parolni Kiriting',
      'Yangi Parol Kiriting',
      'Parolni Tasdiqlang'
    ];

    List<String> subtitles = [
      'Avval joriy parolingizni kiriting',
      'Yangi 4 raqamli parol kiriting',
      'Yangi parolni qayta kiriting'
    ];

    return Container(
      padding: const EdgeInsets.all(20),
      child: Column(
        children: [
          Row(
            children: [
              GestureDetector(
                onTap: () => Navigator.pop(context),
                child: Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    gradient: LinearGradient(
                      begin: Alignment.topLeft,
                      end: Alignment.bottomRight,
                      colors: [
                        cItemColor.withOpacity(0.8),
                        cItemColor.withOpacity(0.6),
                      ],
                    ),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: cButtonColor.withOpacity(0.3),
                      width: 1.5,
                    ),
                    boxShadow: [
                      BoxShadow(
                        color: cButtonColor.withOpacity(0.1),
                        blurRadius: 10,
                        offset: const Offset(0, 4),
                      ),
                    ],
                  ),
                  child: Icon(
                    Icons.arrow_back_ios,
                    color: cWhite,
                    size: 20,
                  ),
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Parolni O\'zgartirish',
                      style: TextStyle(
                        color: cWhite,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      'Xavfsizlik sozlamalari',
                      style: TextStyle(
                        color: cGray,
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),
          const SizedBox(height: 30),
          Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: LinearGradient(
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
                colors: [
                  cItemColor,
                  cItemColor.withOpacity(0.8),
                ],
              ),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: cButtonColor.withOpacity(0.3),
                width: 1.5,
              ),
            ),
            child: Column(
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: cButtonColor.withOpacity(0.2),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Icon(
                        Icons.security,
                        color: cButtonColor,
                        size: 20,
                      ),
                    ),
                    const SizedBox(width: 12),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            titles[_currentStep],
                            style: TextStyle(
                              color: cWhite,
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          Text(
                            subtitles[_currentStep],
                            style: TextStyle(
                              color: cGray,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildStepIndicator(),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStepIndicator() {
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: List.generate(3, (index) {
        bool isActive = index == _currentStep;
        bool isCompleted = index < _currentStep;

        return Container(
          margin: const EdgeInsets.symmetric(horizontal: 4),
          width: 40,
          height: 6,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(3),
            color: isCompleted
                ? Colors.green
                : isActive
                    ? cButtonColor
                    : cGray.withOpacity(0.3),
          ),
        );
      }),
    );
  }

  Widget _buildPasswordDisplay() {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Container(
            width: MediaQuery.of(context).size.width,
            margin: const EdgeInsets.symmetric(horizontal: 20),
            padding: const EdgeInsets.symmetric(vertical: 30, horizontal: 10),
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _hasError ? Colors.red : cButtonColor.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading)
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: cButtonColor,
                      strokeWidth: 2,
                    ),
                  )
                else
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(4, (index) {
                      bool isFilled = index < _getCurrentPassword().length;
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: isFilled
                              ? (_hasError ? Colors.red : cButtonColor)
                              : Colors.transparent,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _hasError ? Colors.red : cGray,
                            width: 2,
                          ),
                        ),
                      );
                    }),
                  ),
                if (_hasError && _errorMessage.isNotEmpty) ...[
                  const SizedBox(height: 10),
                  Text(
                    _errorMessage,
                    style: TextStyle(
                      color: Colors.red,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomKeypad() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        children: [
          // First row: 1, 2, 3
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('1'),
                const SizedBox(width: 15),
                _buildKeypadButton('2'),
                const SizedBox(width: 15),
                _buildKeypadButton('3'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Second row: 4, 5, 6
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('4'),
                const SizedBox(width: 15),
                _buildKeypadButton('5'),
                const SizedBox(width: 15),
                _buildKeypadButton('6'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Third row: 7, 8, 9
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('7'),
                const SizedBox(width: 15),
                _buildKeypadButton('8'),
                const SizedBox(width: 15),
                _buildKeypadButton('9'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Fourth row: Clear, 0, Backspace
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('clear', isSpecial: true),
                const SizedBox(width: 15),
                _buildKeypadButton('0'),
                const SizedBox(width: 15),
                _buildKeypadButton('backspace', isSpecial: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeypadButton(String value, {bool isSpecial = false}) {
    bool isDisabled = _isLoading;

    return Expanded(
      child: GestureDetector(
        onTap: isDisabled ? null : () {
          if (value == 'backspace') {
            _onBackspacePressed();
          } else if (value == 'clear') {
            _onClearPressed();
          } else {
            _onNumberPressed(value);
          }
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          height: 70,
          decoration: BoxDecoration(
            color: isDisabled
                ? (isSpecial ? cItemColor.withOpacity(0.3) : cItemColor.withOpacity(0.5))
                : (isSpecial ? cItemColor.withOpacity(0.6) : cItemColor),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: isDisabled
                  ? cButtonColor.withOpacity(0.1)
                  : cButtonColor.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: isDisabled ? [] : [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Center(
            child: _getKeypadButtonContent(value, isSpecial),
          ),
        ),
      ),
    );
  }

  Widget _getKeypadButtonContent(String value, bool isSpecial) {
    if (value == 'backspace') {
      return Icon(
        Icons.backspace_outlined,
        color: cWhite,
        size: 24,
      );
    } else if (value == 'clear') {
      return Icon(
        Icons.clear,
        color: cWhite,
        size: 24,
      );
    } else {
      return Text(
        value,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: cWhite,
        ),
      );
    }
  }
}
