import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/main.dart';

class AppProvider extends StatefulWidget {
  const AppProvider({super.key});

  @override
  State<AppProvider> createState() => _AppProviderState();
}

class _AppProviderState extends State<AppProvider> with TickerProviderStateMixin {
  final CollectionReference password =
      FirebaseFirestore.instance.collection("password");

  String _enteredPassword = '';
  bool _isLoading = false;
  bool _hasError = false;
  bool _isInputBlocked = false; // Prevent input during async operations

  late AnimationController _shakeController;
  late AnimationController _fadeController;
  late Animation<double> _shakeAnimation;
  late Animation<double> _fadeAnimation;

  @override
  void initState() {
    super.initState();
    _shakeController = AnimationController(
      duration: const Duration(milliseconds: 500),
      vsync: this,
    );
    _fadeController = AnimationController(
      duration: const Duration(milliseconds: 1000),
      vsync: this,
    );

    _shakeAnimation = Tween<double>(begin: 0, end: 10).animate(
      CurvedAnimation(parent: _shakeController, curve: Curves.elasticIn),
    );
    _fadeAnimation = Tween<double>(begin: 0, end: 1).animate(
      CurvedAnimation(parent: _fadeController, curve: Curves.easeInOut),
    );

    _fadeController.forward();
  }

  @override
  void dispose() {
    _shakeController.dispose();
    _fadeController.dispose();
    super.dispose();
  }

  void _onNumberPressed(String number) {
    // Block input during loading or if already processing
    if (_isLoading || _isInputBlocked || _enteredPassword.length >= 6) {
      return;
    }

    setState(() {
      _enteredPassword += number;
      _hasError = false;
    });
    HapticFeedback.lightImpact();

    // Only check password when exactly 4 digits are entered
    if (_enteredPassword.length == 4) {
      _checkPassword();
    }
  }

  void _onBackspacePressed() {
    // Block input during loading
    if (_isLoading || _isInputBlocked) {
      return;
    }

    if (_enteredPassword.isNotEmpty) {
      setState(() {
        _enteredPassword = _enteredPassword.substring(0, _enteredPassword.length - 1);
        _hasError = false;
      });
      HapticFeedback.lightImpact();
    }
  }

  void _onClearPressed() {
    // Block input during loading
    if (_isLoading || _isInputBlocked) {
      return;
    }

    setState(() {
      _enteredPassword = '';
      _hasError = false;
    });
    HapticFeedback.mediumImpact();
  }

  void _checkPassword() async {
    // Prevent multiple simultaneous calls
    if (_isLoading || _isInputBlocked || _enteredPassword.length != 4) {
      return;
    }

    setState(() {
      _isLoading = true;
      _isInputBlocked = true; // Block further input during verification
    });

    try {
      final snapshot = await password.get();
      if (snapshot.docs.isNotEmpty) {
        final DocumentSnapshot documentSnapshot = snapshot.docs[0];
        if (documentSnapshot['password'] == _enteredPassword) {
          HapticFeedback.heavyImpact();
          Navigator.pushReplacement(
            context,
            PageRouteBuilder(
              pageBuilder: (context, animation, secondaryAnimation) => HomePage(),
              transitionsBuilder: (context, animation, secondaryAnimation, child) {
                return FadeTransition(opacity: animation, child: child);
              },
              transitionDuration: const Duration(milliseconds: 500),
            ),
          );
          return; // Don't reset loading state if navigating away
        } else {
          _showError();
        }
      } else {
        _showError();
      }
    } catch (e) {
      _showError();
    }

    setState(() {
      _isLoading = false;
      _isInputBlocked = false; // Re-enable input after verification
    });
  }

  void _showError() {
    setState(() {
      _hasError = true;
      _enteredPassword = '';
      _isLoading = false;
      _isInputBlocked = false; // Re-enable input after error
    });
    HapticFeedback.heavyImpact();
    _shakeController.forward().then((_) {
      _shakeController.reset();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: FadeTransition(
            opacity: _fadeAnimation,
            child: Column(
              children: [
                Expanded(
                  flex: 2,
                  child: _buildHeader(),
                ),
                _buildPasswordDisplay(),
                Expanded(
                  flex: 3,
                  child: _buildCustomKeypad(),
                ),
                const SizedBox(height: 20),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(40),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: cButtonColor.withOpacity(0.2),
              shape: BoxShape.circle,
              border: Border.all(color: cButtonColor, width: 2),
              boxShadow: [
                BoxShadow(
                  color: cButtonColor.withOpacity(0.3),
                  blurRadius: 20,
                  spreadRadius: 5,
                ),
              ],
            ),
            child: Icon(
              Icons.lock_outline,

              color: cButtonColor,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Secure Access',
            style: TextStyle(
              fontSize: 28,
              fontWeight: FontWeight.bold,
              color: cWhite,
              letterSpacing: 1.2,
            ),
          ),
          const SizedBox(height: 10),
          Text(
            'Enter your PIN to continue',
            style: TextStyle(
              fontSize: 16,
              color: cGray,
              fontWeight: FontWeight.w400,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPasswordDisplay() {
    return AnimatedBuilder(
      animation: _shakeAnimation,
      builder: (context, child) {
        return Transform.translate(
          offset: Offset(_shakeAnimation.value, 0),
          child: Container(
            width: MediaQuery.of(context).size.width,
            margin: const EdgeInsets.symmetric(horizontal: 40),
            padding: const EdgeInsets.symmetric(vertical: 40, horizontal: 10),
            decoration: BoxDecoration(
              color: cItemColor.withOpacity(0.8),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: _hasError ? cRed : cButtonColor.withOpacity(0.3),
                width: 2,
              ),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.3),
                  blurRadius: 15,
                  offset: const Offset(0, 5),
                ),
              ],
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (_isLoading)
                  SizedBox(
                    width: 24,
                    height: 24,
                    child: CircularProgressIndicator(
                      color: cButtonColor,
                      strokeWidth: 2,
                    ),
                  )
                else
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: List.generate(4, (index) {
                      bool isFilled = index < _enteredPassword.length;
                      return Container(
                        margin: const EdgeInsets.symmetric(horizontal: 8),
                        width: 16,
                        height: 16,
                        decoration: BoxDecoration(
                          color: isFilled
                              ? (_hasError ? cRed : cButtonColor)
                              : Colors.transparent,
                          shape: BoxShape.circle,
                          border: Border.all(
                            color: _hasError ? cRed : cGray,
                            width: 2,
                          ),
                        ),
                      );
                    }),
                  ),
                if (_hasError) ...[
                  const SizedBox(height: 10),
                  Text(
                    'Incorrect PIN',
                    style: TextStyle(
                      color: cRed,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomKeypad() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 40),
      child: Column(
        children: [
          // First row: 1, 2, 3
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('1'),
                const SizedBox(width: 15),
                _buildKeypadButton('2'),
                const SizedBox(width: 15),
                _buildKeypadButton('3'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Second row: 4, 5, 6
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('4'),
                const SizedBox(width: 15),
                _buildKeypadButton('5'),
                const SizedBox(width: 15),
                _buildKeypadButton('6'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Third row: 7, 8, 9
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('7'),
                const SizedBox(width: 15),
                _buildKeypadButton('8'),
                const SizedBox(width: 15),
                _buildKeypadButton('9'),
              ],
            ),
          ),
          const SizedBox(height: 15),
          // Fourth row: Clear, 0, Backspace
          Expanded(
            child: Row(
              children: [
                _buildKeypadButton('clear', isSpecial: true),
                const SizedBox(width: 15),
                _buildKeypadButton('0'),
                const SizedBox(width: 15),
                _buildKeypadButton('backspace', isSpecial: true),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildKeypadButton(String value, {bool isSpecial = false}) {
    bool isDisabled = _isLoading || _isInputBlocked;

    return Expanded(
      child: GestureDetector(
        onTap: isDisabled ? null : () {
          if (value == 'backspace') {
            _onBackspacePressed();
          } else if (value == 'clear') {
            _onClearPressed();
          } else {
            _onNumberPressed(value);
          }
        },
        child: AnimatedContainer(
          duration: const Duration(milliseconds: 150),
          height: 70,
          decoration: BoxDecoration(
            color: isDisabled
                ? (isSpecial ? cItemColor.withOpacity(0.3) : cItemColor.withOpacity(0.5))
                : (isSpecial ? cItemColor.withOpacity(0.6) : cItemColor),
            borderRadius: BorderRadius.circular(15),
            border: Border.all(
              color: isDisabled
                  ? cButtonColor.withOpacity(0.1)
                  : cButtonColor.withOpacity(0.3),
              width: 1,
            ),
            boxShadow: isDisabled ? [] : [
              BoxShadow(
                color: Colors.black.withOpacity(0.2),
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Center(
            child: _getKeypadButtonContent(value, isSpecial),
          ),
        ),
      ),
    );
  }

  Widget _getKeypadButtonContent(String value, bool isSpecial) {
    if (value == 'backspace') {
      return Icon(
        Icons.backspace_outlined,
        color: cWhite,
        size: 24,
      );
    } else if (value == 'clear') {
      return Icon(
        Icons.clear,
        color: cWhite,
        size: 24,
      );
    } else {
      return Text(
        value,
        style: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.w600,
          color: cWhite,
        ),
      );
    }
  }
}
