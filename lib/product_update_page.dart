import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:pattern_formatter/numeric_formatter.dart';

class ProductUpdatePage extends StatefulWidget {
  final CollectionReference product;
  final Map<String, dynamic>? data;

  const ProductUpdatePage({
    super.key,
    required this.product,
    this.data,
  });

  @override
  State<ProductUpdatePage> createState() => _ProductUpdatePageState();
}

class _ProductUpdatePageState extends State<ProductUpdatePage> {
  TextEditingController nameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController countController = TextEditingController();
  List<ProductItem> listItem = <ProductItem>[];
  ProductItem? productItem;

  @override
  void initState() {
    super.initState();
    if (widget.data != null) {
      nameController = TextEditingController(text: widget.data!['name']);
      priceController = TextEditingController(
          text: NumberFormat("#,###", "en_US").format(widget.data!['price']));

      countController = TextEditingController(
          text: NumberFormat("#,###", "en_US").format(widget.data!['count']));
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Mahsulot o\'zgartirish',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Mahsulot ma\'lumotlarini yangilang',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Mahsulot ma\'lumotlari'),
          const SizedBox(height: 16),
          _buildModernTextField(
            controller: nameController,
            label: 'Mahsulot nomi',
            hint: 'Mahsulot nomini kiriting',
            icon: Icons.inventory_2_outlined,
          ),
          const SizedBox(height: 20),
          _buildModernTextField(
            controller: priceController,
            label: 'Mahsulot sotilish narxi',
            hint: 'Narxni kiriting',
            icon: Icons.payments_outlined,
            keyboardType: TextInputType.number,
            inputFormatters: [ThousandsFormatter()],
            suffix: 'UZS',
          ),
          const SizedBox(height: 20),
          _buildModernTextField(
            controller: countController,
            label: 'Mahsulot soni',
            hint: 'Sonini kiriting',
            icon: Icons.inventory_2_outlined,
            keyboardType: TextInputType.number,
            inputFormatters: [ThousandsFormatter()],
            suffix: 'dona',
          ),
          const SizedBox(height: 32),
          _buildModernButton(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: cWhite,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: cButtonColor,
              size: 16,
            ),
            SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: cButtonColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            style: TextStyle(color: cWhite),
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: cGray),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              suffixText: suffix,
              suffixStyle: TextStyle(
                color: cGray,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernButton() {
    return GestureDetector(
      onTap: () async {
        bool result = await InternetConnectionChecker().hasConnection;
        if (result == true) {
          if (nameController.text.isNotEmpty) {
            if (widget.data != null) {
              widget.product.doc(widget.data!["id"]).update({
                "name": nameController.text,
                "price": int.parse(priceController.text.replaceAll(',', '')),
                "count": int.parse(countController.text.replaceAll(',', ''))
              });
            }
            Navigator.pop(context);
          } else {
            ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(content: Text("Mahsulot nomini kiriting")));
          }
        } else {
          ScaffoldMessenger.of(context)
              .showSnackBar(SnackBar(content: Text("Internet yo'q")));
        }
      },
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cButtonColor,
              cButtonColor.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: cButtonColor.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.save_outlined,
              color: cBlack,
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              widget.data == null ? "Saqlash" : 'O\'zgartirish',
              style: TextStyle(
                color: cBlack,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

setSearchParam(String caseNumber) {
  List<String> caseSearchList = [];
  String temp = "";
  for (int i = 0; i < caseNumber.length; i++) {
    temp = temp + caseNumber[i];
    caseSearchList.add(temp);
  }
  return caseSearchList;
}

class TestItem {
  final String label;
  dynamic value;

  TestItem({required this.label, this.value});

  factory TestItem.fromJson(Map<String, dynamic> json) {
    return TestItem(label: json['label'], value: json['value']);
  }

  @override
  String toString() {
    return 'TestItem{label: $label, value: $value}';
  }
}

class ProductItem {
  String id;
  int count;
  String name;
  int price;

  ProductItem({
    required this.id,
    required this.count,
    required this.name,
    required this.price,
  });

  factory ProductItem.fromJson(QueryDocumentSnapshot json, String id) {
    return ProductItem(
      id: id,
      count: json['count'],
      name: json['name'],
      price: json['price'],
    );
  }

  @override
  String toString() {
    return '$name';
  }
}
