import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Universal text field component with consistent styling and validation
/// Supports different input types, validation, and states
class AppTextField extends StatefulWidget {
  final String? label;
  final String? hint;
  final String? helperText;
  final String? errorText;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final void Function(String)? onSubmitted;
  final void Function()? onTap;
  final TextInputType keyboardType;
  final TextInputAction textInputAction;
  final bool obscureText;
  final bool enabled;
  final bool readOnly;
  final bool autofocus;
  final int? maxLines;
  final int? minLines;
  final int? maxLength;
  final List<TextInputFormatter>? inputFormatters;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final String? prefixText;
  final String? suffixText;
  final FocusNode? focusNode;
  final TextCapitalization textCapitalization;
  final TextAlign textAlign;
  final EdgeInsetsGeometry? contentPadding;
  final AppTextFieldSize size;
  final AppTextFieldVariant variant;

  const AppTextField({
    Key? key,
    this.label,
    this.hint,
    this.helperText,
    this.errorText,
    this.controller,
    this.validator,
    this.onChanged,
    this.onSubmitted,
    this.onTap,
    this.keyboardType = TextInputType.text,
    this.textInputAction = TextInputAction.done,
    this.obscureText = false,
    this.enabled = true,
    this.readOnly = false,
    this.autofocus = false,
    this.maxLines = 1,
    this.minLines,
    this.maxLength,
    this.inputFormatters,
    this.prefixIcon,
    this.suffixIcon,
    this.prefixText,
    this.suffixText,
    this.focusNode,
    this.textCapitalization = TextCapitalization.none,
    this.textAlign = TextAlign.start,
    this.contentPadding,
    this.size = AppTextFieldSize.medium,
    this.variant = AppTextFieldVariant.outlined,
  }) : super(key: key);

  @override
  State<AppTextField> createState() => _AppTextFieldState();
}

class _AppTextFieldState extends State<AppTextField> {
  late bool _obscureText;
  late FocusNode _focusNode;
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _obscureText = widget.obscureText;
    _focusNode = widget.focusNode ?? FocusNode();
    _focusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    _focusNode.removeListener(_onFocusChange);
    if (widget.focusNode == null) {
      _focusNode.dispose();
    }
    super.dispose();
  }

  void _onFocusChange() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        if (widget.label != null) ...[
          Text(
            widget.label!,
            style: _getLabelStyle(),
          ),
          SizedBox(height: AppDimensions.paddingTiny),
        ],
        _buildTextField(),
        if (widget.helperText != null || widget.errorText != null) ...[
          SizedBox(height: AppDimensions.paddingTiny),
          Text(
            widget.errorText ?? widget.helperText ?? '',
            style: widget.errorText != null 
                ? AppTextStyles.errorText 
                : AppTextStyles.bodySmall.copyWith(
                    color: AppColors.onSurfaceVariant,
                  ),
          ),
        ],
      ],
    );
  }

  Widget _buildTextField() {
    switch (widget.variant) {
      case AppTextFieldVariant.outlined:
        return _buildOutlinedTextField();
      case AppTextFieldVariant.filled:
        return _buildFilledTextField();
      case AppTextFieldVariant.underlined:
        return _buildUnderlinedTextField();
    }
  }

  Widget _buildOutlinedTextField() {
    return TextFormField(
      controller: widget.controller,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: _obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      inputFormatters: widget.inputFormatters,
      focusNode: _focusNode,
      textCapitalization: widget.textCapitalization,
      textAlign: widget.textAlign,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: _getHintStyle(),
        prefixIcon: widget.prefixIcon,
        suffixIcon: _buildSuffixIcon(),
        prefixText: widget.prefixText,
        suffixText: widget.suffixText,
        contentPadding: widget.contentPadding ?? _getContentPadding(),
        border: _getOutlinedBorder(),
        enabledBorder: _getOutlinedBorder(),
        focusedBorder: _getOutlinedBorder(focused: true),
        errorBorder: _getOutlinedBorder(error: true),
        focusedErrorBorder: _getOutlinedBorder(error: true, focused: true),
        disabledBorder: _getOutlinedBorder(disabled: true),
        filled: false,
        counterText: widget.maxLength != null ? null : '',
      ),
    );
  }

  Widget _buildFilledTextField() {
    return TextFormField(
      controller: widget.controller,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: _obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      inputFormatters: widget.inputFormatters,
      focusNode: _focusNode,
      textCapitalization: widget.textCapitalization,
      textAlign: widget.textAlign,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: _getHintStyle(),
        prefixIcon: widget.prefixIcon,
        suffixIcon: _buildSuffixIcon(),
        prefixText: widget.prefixText,
        suffixText: widget.suffixText,
        contentPadding: widget.contentPadding ?? _getContentPadding(),
        border: _getFilledBorder(),
        enabledBorder: _getFilledBorder(),
        focusedBorder: _getFilledBorder(focused: true),
        errorBorder: _getFilledBorder(error: true),
        focusedErrorBorder: _getFilledBorder(error: true, focused: true),
        disabledBorder: _getFilledBorder(disabled: true),
        filled: true,
        fillColor: AppColors.surfaceVariant,
        counterText: widget.maxLength != null ? null : '',
      ),
    );
  }

  Widget _buildUnderlinedTextField() {
    return TextFormField(
      controller: widget.controller,
      validator: widget.validator,
      onChanged: widget.onChanged,
      onFieldSubmitted: widget.onSubmitted,
      onTap: widget.onTap,
      keyboardType: widget.keyboardType,
      textInputAction: widget.textInputAction,
      obscureText: _obscureText,
      enabled: widget.enabled,
      readOnly: widget.readOnly,
      autofocus: widget.autofocus,
      maxLines: widget.maxLines,
      minLines: widget.minLines,
      maxLength: widget.maxLength,
      inputFormatters: widget.inputFormatters,
      focusNode: _focusNode,
      textCapitalization: widget.textCapitalization,
      textAlign: widget.textAlign,
      style: _getTextStyle(),
      decoration: InputDecoration(
        hintText: widget.hint,
        hintStyle: _getHintStyle(),
        prefixIcon: widget.prefixIcon,
        suffixIcon: _buildSuffixIcon(),
        prefixText: widget.prefixText,
        suffixText: widget.suffixText,
        contentPadding: widget.contentPadding ?? _getContentPadding(),
        border: _getUnderlineBorder(),
        enabledBorder: _getUnderlineBorder(),
        focusedBorder: _getUnderlineBorder(focused: true),
        errorBorder: _getUnderlineBorder(error: true),
        focusedErrorBorder: _getUnderlineBorder(error: true, focused: true),
        disabledBorder: _getUnderlineBorder(disabled: true),
        filled: false,
        counterText: widget.maxLength != null ? null : '',
      ),
    );
  }

  Widget? _buildSuffixIcon() {
    if (widget.obscureText) {
      return IconButton(
        icon: Icon(
          _obscureText ? Icons.visibility_off : Icons.visibility,
          color: AppColors.onSurfaceVariant,
          size: _getIconSize(),
        ),
        onPressed: () {
          setState(() {
            _obscureText = !_obscureText;
          });
        },
      );
    }
    return widget.suffixIcon;
  }

  OutlineInputBorder _getOutlinedBorder({
    bool focused = false,
    bool error = false,
    bool disabled = false,
  }) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = AppColors.error;
      borderWidth = AppDimensions.borderWidthMedium;
    } else if (focused) {
      borderColor = AppColors.primary;
      borderWidth = AppDimensions.borderWidthMedium;
    } else if (disabled) {
      borderColor = AppColors.outline.withOpacity(0.38);
      borderWidth = AppDimensions.borderWidthThin;
    } else {
      borderColor = AppColors.outline;
      borderWidth = AppDimensions.borderWidthThin;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      borderSide: BorderSide(color: borderColor, width: borderWidth),
    );
  }

  OutlineInputBorder _getFilledBorder({
    bool focused = false,
    bool error = false,
    bool disabled = false,
  }) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = AppColors.error;
      borderWidth = AppDimensions.borderWidthMedium;
    } else if (focused) {
      borderColor = AppColors.primary;
      borderWidth = AppDimensions.borderWidthMedium;
    } else {
      borderColor = Colors.transparent;
      borderWidth = AppDimensions.borderWidthThin;
    }

    return OutlineInputBorder(
      borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
      borderSide: BorderSide(color: borderColor, width: borderWidth),
    );
  }

  UnderlineInputBorder _getUnderlineBorder({
    bool focused = false,
    bool error = false,
    bool disabled = false,
  }) {
    Color borderColor;
    double borderWidth;

    if (error) {
      borderColor = AppColors.error;
      borderWidth = AppDimensions.borderWidthMedium;
    } else if (focused) {
      borderColor = AppColors.primary;
      borderWidth = AppDimensions.borderWidthMedium;
    } else if (disabled) {
      borderColor = AppColors.outline.withOpacity(0.38);
      borderWidth = AppDimensions.borderWidthThin;
    } else {
      borderColor = AppColors.outline;
      borderWidth = AppDimensions.borderWidthThin;
    }

    return UnderlineInputBorder(
      borderSide: BorderSide(color: borderColor, width: borderWidth),
    );
  }

  TextStyle _getLabelStyle() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return AppTextStyles.labelSmall;
      case AppTextFieldSize.medium:
        return AppTextStyles.labelMedium;
      case AppTextFieldSize.large:
        return AppTextStyles.labelLarge;
    }
  }

  TextStyle _getTextStyle() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return AppTextStyles.bodySmall;
      case AppTextFieldSize.medium:
        return AppTextStyles.bodyMedium;
      case AppTextFieldSize.large:
        return AppTextStyles.bodyLarge;
    }
  }

  TextStyle _getHintStyle() {
    return _getTextStyle().copyWith(color: AppColors.onSurfaceVariant);
  }

  EdgeInsetsGeometry _getContentPadding() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        );
      case AppTextFieldSize.medium:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingMedium,
        );
      case AppTextFieldSize.large:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingLarge,
          vertical: AppDimensions.paddingMedium,
        );
    }
  }

  double _getIconSize() {
    switch (widget.size) {
      case AppTextFieldSize.small:
        return AppDimensions.iconSmall;
      case AppTextFieldSize.medium:
        return AppDimensions.iconMedium;
      case AppTextFieldSize.large:
        return AppDimensions.iconMedium;
    }
  }
}

enum AppTextFieldSize {
  small,
  medium,
  large,
}

enum AppTextFieldVariant {
  outlined,
  filled,
  underlined,
}
