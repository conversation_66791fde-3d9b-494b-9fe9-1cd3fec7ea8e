import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';
import 'app_text_field.dart';

/// Password input field with strength validation and visibility toggle
class PasswordField extends StatefulWidget {
  final String? label;
  final String? hint;
  final TextEditingController? controller;
  final String? Function(String?)? validator;
  final void Function(String)? onChanged;
  final bool showStrengthIndicator;
  final bool enabled;
  final bool autofocus;
  final FocusNode? focusNode;
  final AppTextFieldSize size;
  final AppTextFieldVariant variant;

  const PasswordField({
    Key? key,
    this.label,
    this.hint,
    this.controller,
    this.validator,
    this.onChanged,
    this.showStrengthIndicator = false,
    this.enabled = true,
    this.autofocus = false,
    this.focusNode,
    this.size = AppTextFieldSize.medium,
    this.variant = AppTextFieldVariant.outlined,
  }) : super(key: key);

  @override
  State<PasswordField> createState() => _PasswordFieldState();
}

class _PasswordFieldState extends State<PasswordField> {
  bool _obscureText = true;
  PasswordStrength _strength = PasswordStrength.none;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        AppTextField(
          label: widget.label,
          hint: widget.hint ?? 'Enter password',
          controller: widget.controller,
          validator: widget.validator,
          onChanged: _onPasswordChanged,
          obscureText: _obscureText,
          enabled: widget.enabled,
          autofocus: widget.autofocus,
          focusNode: widget.focusNode,
          size: widget.size,
          variant: widget.variant,
          suffixIcon: IconButton(
            icon: Icon(
              _obscureText ? Icons.visibility_off : Icons.visibility,
              color: AppColors.onSurfaceVariant,
            ),
            onPressed: () {
              setState(() {
                _obscureText = !_obscureText;
              });
            },
          ),
        ),
        if (widget.showStrengthIndicator) ...[
          SizedBox(height: AppDimensions.paddingSmall),
          _buildStrengthIndicator(),
          SizedBox(height: AppDimensions.paddingTiny),
          _buildStrengthText(),
        ],
      ],
    );
  }

  void _onPasswordChanged(String password) {
    setState(() {
      _strength = _calculatePasswordStrength(password);
    });
    widget.onChanged?.call(password);
  }

  Widget _buildStrengthIndicator() {
    return Row(
      children: [
        Expanded(
          child: Container(
            height: 4,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(2),
              color: AppColors.outline.withOpacity(0.3),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerLeft,
              widthFactor: _getStrengthProgress(),
              child: Container(
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(2),
                  color: _getStrengthColor(),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildStrengthText() {
    return Text(
      _getStrengthText(),
      style: AppTextStyles.bodySmall.copyWith(
        color: _getStrengthColor(),
        fontWeight: FontWeight.w500,
      ),
    );
  }

  PasswordStrength _calculatePasswordStrength(String password) {
    if (password.isEmpty) return PasswordStrength.none;
    
    int score = 0;
    
    // Length check
    if (password.length >= 8) score++;
    if (password.length >= 12) score++;
    
    // Character variety checks
    if (password.contains(RegExp(r'[a-z]'))) score++; // lowercase
    if (password.contains(RegExp(r'[A-Z]'))) score++; // uppercase
    if (password.contains(RegExp(r'[0-9]'))) score++; // numbers
    if (password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) score++; // special chars
    
    // Additional complexity checks
    if (password.length >= 16) score++;
    if (!password.contains(RegExp(r'(.)\1{2,}'))) score++; // no repeated chars
    
    if (score <= 2) return PasswordStrength.weak;
    if (score <= 4) return PasswordStrength.medium;
    if (score <= 6) return PasswordStrength.strong;
    return PasswordStrength.veryStrong;
  }

  double _getStrengthProgress() {
    switch (_strength) {
      case PasswordStrength.none:
        return 0.0;
      case PasswordStrength.weak:
        return 0.25;
      case PasswordStrength.medium:
        return 0.5;
      case PasswordStrength.strong:
        return 0.75;
      case PasswordStrength.veryStrong:
        return 1.0;
    }
  }

  Color _getStrengthColor() {
    switch (_strength) {
      case PasswordStrength.none:
        return AppColors.outline;
      case PasswordStrength.weak:
        return AppColors.error;
      case PasswordStrength.medium:
        return AppColors.warning;
      case PasswordStrength.strong:
        return AppColors.info;
      case PasswordStrength.veryStrong:
        return AppColors.success;
    }
  }

  String _getStrengthText() {
    switch (_strength) {
      case PasswordStrength.none:
        return '';
      case PasswordStrength.weak:
        return 'Weak password';
      case PasswordStrength.medium:
        return 'Medium strength';
      case PasswordStrength.strong:
        return 'Strong password';
      case PasswordStrength.veryStrong:
        return 'Very strong password';
    }
  }
}

enum PasswordStrength {
  none,
  weak,
  medium,
  strong,
  veryStrong,
}

/// Password validation utilities
class PasswordValidator {
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }
    
    if (password.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    
    if (!password.contains(RegExp(r'[a-z]'))) {
      return 'Password must contain at least one lowercase letter';
    }
    
    if (!password.contains(RegExp(r'[A-Z]'))) {
      return 'Password must contain at least one uppercase letter';
    }
    
    if (!password.contains(RegExp(r'[0-9]'))) {
      return 'Password must contain at least one number';
    }
    
    if (!password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]'))) {
      return 'Password must contain at least one special character';
    }
    
    return null;
  }
  
  static String? validatePasswordConfirmation(String? password, String? confirmation) {
    if (confirmation == null || confirmation.isEmpty) {
      return 'Please confirm your password';
    }
    
    if (password != confirmation) {
      return 'Passwords do not match';
    }
    
    return null;
  }
  
  static List<String> getPasswordRequirements() {
    return [
      'At least 8 characters long',
      'Contains uppercase letter (A-Z)',
      'Contains lowercase letter (a-z)',
      'Contains number (0-9)',
      'Contains special character (!@#\$%^&*)',
    ];
  }
  
  static List<bool> checkPasswordRequirements(String password) {
    return [
      password.length >= 8,
      password.contains(RegExp(r'[A-Z]')),
      password.contains(RegExp(r'[a-z]')),
      password.contains(RegExp(r'[0-9]')),
      password.contains(RegExp(r'[!@#$%^&*(),.?":{}|<>]')),
    ];
  }
}

/// Widget to display password requirements with checkmarks
class PasswordRequirements extends StatelessWidget {
  final String password;
  final bool showOnlyFailed;

  const PasswordRequirements({
    Key? key,
    required this.password,
    this.showOnlyFailed = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final requirements = PasswordValidator.getPasswordRequirements();
    final checks = PasswordValidator.checkPasswordRequirements(password);
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Password Requirements:',
          style: AppTextStyles.labelMedium.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        SizedBox(height: AppDimensions.paddingSmall),
        ...List.generate(requirements.length, (index) {
          final isValid = checks[index];
          
          if (showOnlyFailed && isValid) {
            return const SizedBox.shrink();
          }
          
          return Padding(
            padding: EdgeInsets.only(bottom: AppDimensions.paddingTiny),
            child: Row(
              children: [
                Icon(
                  isValid ? Icons.check_circle : Icons.radio_button_unchecked,
                  size: AppDimensions.iconSmall,
                  color: isValid ? AppColors.success : AppColors.onSurfaceVariant,
                ),
                SizedBox(width: AppDimensions.paddingSmall),
                Expanded(
                  child: Text(
                    requirements[index],
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isValid ? AppColors.success : AppColors.onSurfaceVariant,
                      decoration: isValid ? TextDecoration.lineThrough : null,
                    ),
                  ),
                ),
              ],
            ),
          );
        }),
      ],
    );
  }
}
