import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Universal button component with consistent styling and behavior
/// Supports different button types, sizes, and states
class AppButton extends StatelessWidget {
  final String text;
  final VoidCallback? onPressed;
  final AppButtonType type;
  final AppButtonSize size;
  final bool isLoading;
  final bool isDisabled;
  final Widget? icon;
  final bool isIconLeading;
  final Color? customColor;
  final double? width;
  final EdgeInsetsGeometry? padding;
  final BorderRadius? borderRadius;

  const AppButton({
    Key? key,
    required this.text,
    this.onPressed,
    this.type = AppButtonType.primary,
    this.size = AppButtonSize.medium,
    this.isLoading = false,
    this.isDisabled = false,
    this.icon,
    this.isIconLeading = true,
    this.customColor,
    this.width,
    this.padding,
    this.borderRadius,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final isEffectivelyDisabled = isDisabled || onPressed == null;
    
    return SizedBox(
      width: width,
      height: _getButtonHeight(),
      child: _buildButton(context, isEffectivelyDisabled),
    );
  }

  Widget _buildButton(BuildContext context, bool isEffectivelyDisabled) {
    switch (type) {
      case AppButtonType.primary:
        return _buildElevatedButton(context, isEffectivelyDisabled);
      case AppButtonType.secondary:
        return _buildOutlinedButton(context, isEffectivelyDisabled);
      case AppButtonType.tertiary:
        return _buildTextButton(context, isEffectivelyDisabled);
      case AppButtonType.danger:
        return _buildDangerButton(context, isEffectivelyDisabled);
      case AppButtonType.success:
        return _buildSuccessButton(context, isEffectivelyDisabled);
    }
  }

  Widget _buildElevatedButton(BuildContext context, bool isEffectivelyDisabled) {
    return ElevatedButton(
      onPressed: isEffectivelyDisabled ? null : _handlePress,
      style: ElevatedButton.styleFrom(
        backgroundColor: customColor ?? AppColors.primary,
        foregroundColor: AppColors.onPrimary,
        disabledBackgroundColor: AppColors.onSurface.withOpacity(0.12),
        disabledForegroundColor: AppColors.onSurface.withOpacity(0.38),
        elevation: AppDimensions.elevationSmall,
        padding: padding ?? _getButtonPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        textStyle: _getTextStyle(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildOutlinedButton(BuildContext context, bool isEffectivelyDisabled) {
    final borderColor = customColor ?? AppColors.primary;
    return OutlinedButton(
      onPressed: isEffectivelyDisabled ? null : _handlePress,
      style: OutlinedButton.styleFrom(
        foregroundColor: borderColor,
        disabledForegroundColor: AppColors.onSurface.withOpacity(0.38),
        side: BorderSide(
          color: isEffectivelyDisabled 
              ? AppColors.onSurface.withOpacity(0.12) 
              : borderColor,
          width: AppDimensions.borderWidthMedium,
        ),
        padding: padding ?? _getButtonPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        textStyle: _getTextStyle(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildTextButton(BuildContext context, bool isEffectivelyDisabled) {
    return TextButton(
      onPressed: isEffectivelyDisabled ? null : _handlePress,
      style: TextButton.styleFrom(
        foregroundColor: customColor ?? AppColors.primary,
        disabledForegroundColor: AppColors.onSurface.withOpacity(0.38),
        padding: padding ?? _getButtonPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        textStyle: _getTextStyle(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildDangerButton(BuildContext context, bool isEffectivelyDisabled) {
    return ElevatedButton(
      onPressed: isEffectivelyDisabled ? null : _handlePress,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.error,
        foregroundColor: AppColors.onError,
        disabledBackgroundColor: AppColors.onSurface.withOpacity(0.12),
        disabledForegroundColor: AppColors.onSurface.withOpacity(0.38),
        elevation: AppDimensions.elevationSmall,
        padding: padding ?? _getButtonPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        textStyle: _getTextStyle(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildSuccessButton(BuildContext context, bool isEffectivelyDisabled) {
    return ElevatedButton(
      onPressed: isEffectivelyDisabled ? null : _handlePress,
      style: ElevatedButton.styleFrom(
        backgroundColor: AppColors.success,
        foregroundColor: AppColors.onSuccess,
        disabledBackgroundColor: AppColors.onSurface.withOpacity(0.12),
        disabledForegroundColor: AppColors.onSurface.withOpacity(0.38),
        elevation: AppDimensions.elevationSmall,
        padding: padding ?? _getButtonPadding(),
        shape: RoundedRectangleBorder(
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        ),
        textStyle: _getTextStyle(),
      ),
      child: _buildButtonContent(),
    );
  }

  Widget _buildButtonContent() {
    if (isLoading) {
      return SizedBox(
        width: _getIconSize(),
        height: _getIconSize(),
        child: CircularProgressIndicator(
          strokeWidth: 2,
          valueColor: AlwaysStoppedAnimation<Color>(
            type == AppButtonType.primary || type == AppButtonType.danger || type == AppButtonType.success
                ? AppColors.onPrimary
                : AppColors.primary,
          ),
        ),
      );
    }

    if (icon != null) {
      return Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (isIconLeading) ...[
            SizedBox(
              width: _getIconSize(),
              height: _getIconSize(),
              child: icon,
            ),
            SizedBox(width: AppDimensions.paddingSmall),
          ],
          Flexible(
            child: Text(
              text,
              overflow: TextOverflow.ellipsis,
              maxLines: 1,
            ),
          ),
          if (!isIconLeading) ...[
            SizedBox(width: AppDimensions.paddingSmall),
            SizedBox(
              width: _getIconSize(),
              height: _getIconSize(),
              child: icon,
            ),
          ],
        ],
      );
    }

    return Text(
      text,
      overflow: TextOverflow.ellipsis,
      maxLines: 1,
    );
  }

  void _handlePress() {
    if (!isLoading && !isDisabled && onPressed != null) {
      onPressed!();
    }
  }

  double _getButtonHeight() {
    switch (size) {
      case AppButtonSize.small:
        return AppDimensions.buttonHeightSmall;
      case AppButtonSize.medium:
        return AppDimensions.buttonHeightMedium;
      case AppButtonSize.large:
        return AppDimensions.buttonHeightLarge;
      case AppButtonSize.extraLarge:
        return AppDimensions.buttonHeightXLarge;
    }
  }

  EdgeInsetsGeometry _getButtonPadding() {
    switch (size) {
      case AppButtonSize.small:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        );
      case AppButtonSize.medium:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingLarge,
          vertical: AppDimensions.paddingMedium,
        );
      case AppButtonSize.large:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingXLarge,
          vertical: AppDimensions.paddingMedium,
        );
      case AppButtonSize.extraLarge:
        return EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingXLarge,
          vertical: AppDimensions.paddingLarge,
        );
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case AppButtonSize.small:
        return AppTextStyles.labelMedium;
      case AppButtonSize.medium:
        return AppTextStyles.labelLarge;
      case AppButtonSize.large:
        return AppTextStyles.titleSmall;
      case AppButtonSize.extraLarge:
        return AppTextStyles.titleMedium;
    }
  }

  double _getIconSize() {
    switch (size) {
      case AppButtonSize.small:
        return AppDimensions.iconSmall;
      case AppButtonSize.medium:
        return AppDimensions.iconMedium;
      case AppButtonSize.large:
        return AppDimensions.iconMedium;
      case AppButtonSize.extraLarge:
        return AppDimensions.iconLarge;
    }
  }
}

enum AppButtonType {
  primary,
  secondary,
  tertiary,
  danger,
  success,
}

enum AppButtonSize {
  small,
  medium,
  large,
  extraLarge,
}
