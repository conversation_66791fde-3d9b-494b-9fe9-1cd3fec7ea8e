import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Professional list item with modern design and swipe actions
class AppListItem extends StatelessWidget {
  final String title;
  final String? subtitle;
  final String? trailing;
  final Widget? leadingIcon;
  final Widget? trailingWidget;
  final VoidCallback? onTap;
  final List<AppSlidableAction>? slidableActions;
  final Color? backgroundColor;
  final bool isSelected;
  final bool showDivider;

  const AppListItem({
    Key? key,
    required this.title,
    this.subtitle,
    this.trailing,
    this.leadingIcon,
    this.trailingWidget,
    this.onTap,
    this.slidableActions,
    this.backgroundColor,
    this.isSelected = false,
    this.showDivider = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    Widget listTile = Container(
      color: backgroundColor ?? (isSelected ? AppColors.primaryContainer.withOpacity(0.3) : null),
      child: Column(
        children: [
          ListTile(
            leading: leadingIcon,
            title: Text(
              title,
              style: AppTextStyles.titleSmall.copyWith(
                fontWeight: FontWeight.w500,
                color: isSelected ? AppColors.primary : null,
              ),
            ),
            subtitle: subtitle != null
                ? Text(
                    subtitle!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  )
                : null,
            trailing: trailingWidget ?? (trailing != null
                ? Text(
                    trailing!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                      fontWeight: FontWeight.w500,
                    ),
                  )
                : null),
            onTap: onTap,
            contentPadding: EdgeInsets.symmetric(
              horizontal: AppDimensions.paddingMedium,
              vertical: AppDimensions.paddingSmall,
            ),
          ),
          if (showDivider)
            Divider(
              height: 1,
              color: AppColors.outline.withOpacity(0.2),
              indent: AppDimensions.paddingMedium,
              endIndent: AppDimensions.paddingMedium,
            ),
        ],
      ),
    );

    if (slidableActions != null && slidableActions!.isNotEmpty) {
      return Slidable(
        key: ValueKey(title),
        endActionPane: ActionPane(
          motion: const ScrollMotion(),
          children: slidableActions!.map((action) => SlidableAction(
            onPressed: (context) => action.onPressed(),
            backgroundColor: action.backgroundColor,
            foregroundColor: action.foregroundColor,
            icon: action.icon,
            label: action.label,
          )).toList(),
        ),
        child: listTile,
      );
    }

    return listTile;
  }
}

/// Slidable action model
class AppSlidableAction {
  final VoidCallback onPressed;
  final Color backgroundColor;
  final Color foregroundColor;
  final IconData icon;
  final String label;

  const AppSlidableAction({
    required this.onPressed,
    required this.backgroundColor,
    required this.foregroundColor,
    required this.icon,
    required this.label,
  });
}

/// Professional product list item
class ProductListItem extends StatelessWidget {
  final String productName;
  final String? productCode;
  final double price;
  final int? quantity;
  final String? category;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;
  final bool isLowStock;

  const ProductListItem({
    Key? key,
    required this.productName,
    this.productCode,
    required this.price,
    this.quantity,
    this.category,
    this.onTap,
    this.onEdit,
    this.onDelete,
    this.isLowStock = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppListItem(
      title: productName,
      subtitle: _buildSubtitle(),
      leadingIcon: Container(
        padding: EdgeInsets.all(AppDimensions.paddingSmall),
        decoration: BoxDecoration(
          color: isLowStock ? AppColors.warning.withOpacity(0.1) : AppColors.primaryContainer,
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        ),
        child: Icon(
          Icons.inventory_2_outlined,
          color: isLowStock ? AppColors.warning : AppColors.onPrimaryContainer,
          size: AppDimensions.iconMedium,
        ),
      ),
      trailingWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '\$${price.toStringAsFixed(2)}',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.primary,
            ),
          ),
          if (quantity != null) ...[
            SizedBox(height: AppDimensions.paddingTiny),
            Container(
              padding: EdgeInsets.symmetric(
                horizontal: AppDimensions.paddingSmall,
                vertical: AppDimensions.paddingTiny,
              ),
              decoration: BoxDecoration(
                color: isLowStock ? AppColors.warning : AppColors.success,
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              ),
              child: Text(
                'Qty: $quantity',
                style: AppTextStyles.labelSmall.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ],
      ),
      onTap: onTap,
      slidableActions: [
        if (onEdit != null)
          AppSlidableAction(
            onPressed: onEdit!,
            backgroundColor: AppColors.info,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Edit',
          ),
        if (onDelete != null)
          AppSlidableAction(
            onPressed: onDelete!,
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Delete',
          ),
      ],
    );
  }

  String _buildSubtitle() {
    List<String> parts = [];
    if (productCode != null) parts.add('Code: $productCode');
    if (category != null) parts.add('Category: $category');
    return parts.join(' • ');
  }
}

/// Professional transaction list item
class TransactionListItem extends StatelessWidget {
  final String transactionId;
  final DateTime date;
  final double amount;
  final String? customerName;
  final List<String> productNames;
  final bool isPaid;
  final VoidCallback? onTap;
  final VoidCallback? onDelete;

  const TransactionListItem({
    Key? key,
    required this.transactionId,
    required this.date,
    required this.amount,
    this.customerName,
    required this.productNames,
    this.isPaid = false,
    this.onTap,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppListItem(
      title: customerName ?? 'Transaction #${transactionId.substring(0, 8)}',
      subtitle: _buildSubtitle(),
      leadingIcon: Container(
        padding: EdgeInsets.all(AppDimensions.paddingSmall),
        decoration: BoxDecoration(
          color: isPaid ? AppColors.success.withOpacity(0.1) : AppColors.warning.withOpacity(0.1),
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        ),
        child: Icon(
          isPaid ? Icons.check_circle_outline : Icons.pending_outlined,
          color: isPaid ? AppColors.success : AppColors.warning,
          size: AppDimensions.iconMedium,
        ),
      ),
      trailingWidget: Column(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Text(
            '\$${amount.toStringAsFixed(2)}',
            style: AppTextStyles.titleSmall.copyWith(
              fontWeight: FontWeight.w600,
              color: isPaid ? AppColors.success : AppColors.warning,
            ),
          ),
          SizedBox(height: AppDimensions.paddingTiny),
          Text(
            _formatDate(date),
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
      onTap: onTap,
      slidableActions: onDelete != null ? [
        AppSlidableAction(
          onPressed: onDelete!,
          backgroundColor: AppColors.error,
          foregroundColor: Colors.white,
          icon: Icons.delete,
          label: 'Delete',
        ),
      ] : null,
    );
  }

  String _buildSubtitle() {
    String products = productNames.take(2).join(', ');
    if (productNames.length > 2) {
      products += ' +${productNames.length - 2} more';
    }
    return products;
  }

  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);
    
    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}

/// Professional customer list item
class CustomerListItem extends StatelessWidget {
  final String customerName;
  final String? phone;
  final String? email;
  final double? totalPurchases;
  final int? transactionCount;
  final VoidCallback? onTap;
  final VoidCallback? onEdit;
  final VoidCallback? onDelete;

  const CustomerListItem({
    Key? key,
    required this.customerName,
    this.phone,
    this.email,
    this.totalPurchases,
    this.transactionCount,
    this.onTap,
    this.onEdit,
    this.onDelete,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppListItem(
      title: customerName,
      subtitle: _buildSubtitle(),
      leadingIcon: CircleAvatar(
        backgroundColor: AppColors.primaryContainer,
        child: Text(
          customerName.isNotEmpty ? customerName[0].toUpperCase() : 'C',
          style: AppTextStyles.titleSmall.copyWith(
            color: AppColors.onPrimaryContainer,
            fontWeight: FontWeight.w600,
          ),
        ),
      ),
      trailingWidget: totalPurchases != null
          ? Column(
              crossAxisAlignment: CrossAxisAlignment.end,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  '\$${totalPurchases!.toStringAsFixed(2)}',
                  style: AppTextStyles.titleSmall.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppColors.primary,
                  ),
                ),
                if (transactionCount != null) ...[
                  SizedBox(height: AppDimensions.paddingTiny),
                  Text(
                    '$transactionCount orders',
                    style: AppTextStyles.bodySmall.copyWith(
                      color: AppColors.onSurfaceVariant,
                    ),
                  ),
                ],
              ],
            )
          : null,
      onTap: onTap,
      slidableActions: [
        if (onEdit != null)
          AppSlidableAction(
            onPressed: onEdit!,
            backgroundColor: AppColors.info,
            foregroundColor: Colors.white,
            icon: Icons.edit,
            label: 'Edit',
          ),
        if (onDelete != null)
          AppSlidableAction(
            onPressed: onDelete!,
            backgroundColor: AppColors.error,
            foregroundColor: Colors.white,
            icon: Icons.delete,
            label: 'Delete',
          ),
      ],
    );
  }

  String _buildSubtitle() {
    List<String> parts = [];
    if (phone != null) parts.add(phone!);
    if (email != null) parts.add(email!);
    return parts.join(' • ');
  }
}
