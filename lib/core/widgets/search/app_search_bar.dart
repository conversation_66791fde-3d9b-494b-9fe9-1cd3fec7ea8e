import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Professional search bar with modern design
class AppSearchBar extends StatefulWidget {
  final String? hintText;
  final TextEditingController? controller;
  final Function(String)? onChanged;
  final Function(String)? onSubmitted;
  final VoidCallback? onClear;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final bool autofocus;
  final bool enabled;

  const AppSearchBar({
    Key? key,
    this.hintText,
    this.controller,
    this.onChanged,
    this.onSubmitted,
    this.onClear,
    this.prefixIcon,
    this.suffixIcon,
    this.autofocus = false,
    this.enabled = true,
  }) : super(key: key);

  @override
  State<AppSearchBar> createState() => _AppSearchBarState();
}

class _AppSearchBarState extends State<AppSearchBar> {
  late TextEditingController _controller;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _controller = widget.controller ?? TextEditingController();
    _hasText = _controller.text.isNotEmpty;
    _controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    if (widget.controller == null) {
      _controller.dispose();
    } else {
      _controller.removeListener(_onTextChanged);
    }
    super.dispose();
  }

  void _onTextChanged() {
    final hasText = _controller.text.isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
    }
  }

  void _onClear() {
    _controller.clear();
    widget.onChanged?.call('');
    widget.onClear?.call();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.getSurface(context),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: AppColors.outline.withOpacity(0.3),
          width: 1,
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.05),
            blurRadius: AppDimensions.shadowBlurSmall,
            offset: const Offset(0, 1),
          ),
        ],
      ),
      child: TextField(
        controller: _controller,
        onChanged: widget.onChanged,
        onSubmitted: widget.onSubmitted,
        autofocus: widget.autofocus,
        enabled: widget.enabled,
        style: AppTextStyles.bodyMedium,
        decoration: InputDecoration(
          hintText: widget.hintText ?? 'Search...',
          hintStyle: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.onSurfaceVariant,
          ),
          prefixIcon: widget.prefixIcon ?? Icon(
            Icons.search,
            color: AppColors.onSurfaceVariant,
            size: AppDimensions.iconMedium,
          ),
          suffixIcon: _hasText
              ? IconButton(
                  onPressed: _onClear,
                  icon: Icon(
                    Icons.clear,
                    color: AppColors.onSurfaceVariant,
                    size: AppDimensions.iconMedium,
                  ),
                )
              : widget.suffixIcon,
          border: InputBorder.none,
          contentPadding: EdgeInsets.symmetric(
            horizontal: AppDimensions.paddingMedium,
            vertical: AppDimensions.paddingMedium,
          ),
        ),
      ),
    );
  }
}

/// Professional filter chip component
class AppFilterChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback? onTap;
  final IconData? icon;
  final Color? selectedColor;

  const AppFilterChip({
    Key? key,
    required this.label,
    this.isSelected = false,
    this.onTap,
    this.icon,
    this.selectedColor,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final effectiveSelectedColor = selectedColor ?? AppColors.primary;
    
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
      child: AnimatedContainer(
        duration: Duration(milliseconds: AppDimensions.animationDurationFast),
        padding: EdgeInsets.symmetric(
          horizontal: AppDimensions.paddingMedium,
          vertical: AppDimensions.paddingSmall,
        ),
        decoration: BoxDecoration(
          color: isSelected 
              ? effectiveSelectedColor 
              : AppColors.getSurface(context),
          borderRadius: BorderRadius.circular(AppDimensions.radiusLarge),
          border: Border.all(
            color: isSelected 
                ? effectiveSelectedColor 
                : AppColors.outline.withOpacity(0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            if (icon != null) ...[
              Icon(
                icon,
                size: AppDimensions.iconSmall,
                color: isSelected 
                    ? Colors.white 
                    : AppColors.onSurfaceVariant,
              ),
              SizedBox(width: AppDimensions.paddingSmall),
            ],
            Text(
              label,
              style: AppTextStyles.labelMedium.copyWith(
                color: isSelected 
                    ? Colors.white 
                    : AppColors.onSurfaceVariant,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Professional filter bar with multiple filter options
class AppFilterBar extends StatelessWidget {
  final List<AppFilterOption> filters;
  final Function(String, bool)? onFilterChanged;
  final VoidCallback? onClearAll;
  final bool showClearAll;

  const AppFilterBar({
    Key? key,
    required this.filters,
    this.onFilterChanged,
    this.onClearAll,
    this.showClearAll = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final hasActiveFilters = filters.any((filter) => filter.isSelected);
    
    return Container(
      padding: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingMedium,
        vertical: AppDimensions.paddingSmall,
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Filters',
                style: AppTextStyles.titleSmall.copyWith(
                  fontWeight: FontWeight.w600,
                ),
              ),
              Spacer(),
              if (showClearAll && hasActiveFilters)
                TextButton(
                  onPressed: onClearAll,
                  child: Text(
                    'Clear All',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.primary,
                    ),
                  ),
                ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingSmall),
          Wrap(
            spacing: AppDimensions.paddingSmall,
            runSpacing: AppDimensions.paddingSmall,
            children: filters.map((filter) => AppFilterChip(
              label: filter.label,
              isSelected: filter.isSelected,
              icon: filter.icon,
              selectedColor: filter.selectedColor,
              onTap: () => onFilterChanged?.call(filter.key, !filter.isSelected),
            )).toList(),
          ),
        ],
      ),
    );
  }
}

/// Filter option model
class AppFilterOption {
  final String key;
  final String label;
  final bool isSelected;
  final IconData? icon;
  final Color? selectedColor;

  const AppFilterOption({
    required this.key,
    required this.label,
    this.isSelected = false,
    this.icon,
    this.selectedColor,
  });

  AppFilterOption copyWith({
    String? key,
    String? label,
    bool? isSelected,
    IconData? icon,
    Color? selectedColor,
  }) {
    return AppFilterOption(
      key: key ?? this.key,
      label: label ?? this.label,
      isSelected: isSelected ?? this.isSelected,
      icon: icon ?? this.icon,
      selectedColor: selectedColor ?? this.selectedColor,
    );
  }
}

/// Professional sort dropdown
class AppSortDropdown extends StatelessWidget {
  final String? selectedValue;
  final List<AppSortOption> options;
  final Function(String?)? onChanged;
  final String? label;

  const AppSortDropdown({
    Key? key,
    this.selectedValue,
    required this.options,
    this.onChanged,
    this.label,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.getSurface(context),
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(
          color: AppColors.outline.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: DropdownButtonHideUnderline(
        child: DropdownButton<String>(
          value: selectedValue,
          onChanged: onChanged,
          hint: Text(
            label ?? 'Sort by',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
          icon: Icon(
            Icons.keyboard_arrow_down,
            color: AppColors.onSurfaceVariant,
          ),
          items: options.map((option) => DropdownMenuItem<String>(
            value: option.value,
            child: Row(
              children: [
                if (option.icon != null) ...[
                  Icon(
                    option.icon,
                    size: AppDimensions.iconSmall,
                    color: AppColors.onSurfaceVariant,
                  ),
                  SizedBox(width: AppDimensions.paddingSmall),
                ],
                Text(
                  option.label,
                  style: AppTextStyles.bodyMedium,
                ),
              ],
            ),
          )).toList(),
        ),
      ),
    );
  }
}

/// Sort option model
class AppSortOption {
  final String value;
  final String label;
  final IconData? icon;

  const AppSortOption({
    required this.value,
    required this.label,
    this.icon,
  });
}
