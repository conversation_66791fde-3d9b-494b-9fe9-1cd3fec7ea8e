import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Universal card component with consistent styling and behavior
class AppCard extends StatelessWidget {
  final Widget child;
  final String? title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? padding;
  final EdgeInsetsGeometry? margin;
  final double? elevation;
  final Color? color;
  final BorderRadius? borderRadius;
  final Border? border;
  final AppCardVariant variant;

  const AppCard({
    Key? key,
    required this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
    this.elevation,
    this.color,
    this.borderRadius,
    this.border,
    this.variant = AppCardVariant.elevated,
  }) : super(key: key);

  const AppCard.outlined({
    Key? key,
    required this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
    this.color,
    this.borderRadius,
    this.border,
  }) : variant = AppCardVariant.outlined,
       elevation = 0,
       super(key: key);

  const AppCard.filled({
    Key? key,
    required this.child,
    this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.padding,
    this.margin,
    this.elevation,
    this.color,
    this.borderRadius,
    this.border,
  }) : variant = AppCardVariant.filled,
       super(key: key);

  @override
  Widget build(BuildContext context) {
    final cardChild = _buildCardContent();
    
    switch (variant) {
      case AppCardVariant.elevated:
        return _buildElevatedCard(cardChild);
      case AppCardVariant.outlined:
        return _buildOutlinedCard(cardChild);
      case AppCardVariant.filled:
        return _buildFilledCard(cardChild);
    }
  }

  Widget _buildCardContent() {
    if (title != null || subtitle != null || leading != null || trailing != null) {
      return Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (title != null || subtitle != null || leading != null || trailing != null)
            _buildHeader(),
          if ((title != null || subtitle != null || leading != null || trailing != null))
            SizedBox(height: AppDimensions.paddingMedium),
          child,
        ],
      );
    }
    
    return child;
  }

  Widget _buildHeader() {
    return Row(
      children: [
        if (leading != null) ...[
          leading!,
          SizedBox(width: AppDimensions.paddingMedium),
        ],
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              if (title != null)
                Text(
                  title!,
                  style: AppTextStyles.cardTitle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              if (subtitle != null) ...[
                if (title != null) SizedBox(height: AppDimensions.paddingTiny),
                Text(
                  subtitle!,
                  style: AppTextStyles.cardSubtitle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ],
          ),
        ),
        if (trailing != null) ...[
          SizedBox(width: AppDimensions.paddingMedium),
          trailing!,
        ],
      ],
    );
  }

  Widget _buildElevatedCard(Widget cardChild) {
    return Container(
      margin: margin ?? EdgeInsets.all(AppDimensions.paddingSmall),
      child: Material(
        color: color ?? AppColors.surface,
        elevation: elevation ?? AppDimensions.elevationSmall,
        borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
          child: Container(
            padding: padding ?? EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: border != null
                ? BoxDecoration(
                    border: border,
                    borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
                  )
                : null,
            child: cardChild,
          ),
        ),
      ),
    );
  }

  Widget _buildOutlinedCard(Widget cardChild) {
    return Container(
      margin: margin ?? EdgeInsets.all(AppDimensions.paddingSmall),
      child: Material(
        color: color ?? AppColors.surface,
        borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
          child: Container(
            padding: padding ?? EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: BoxDecoration(
              border: border ?? Border.all(
                color: AppColors.outline,
                width: AppDimensions.borderWidthThin,
              ),
              borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
            ),
            child: cardChild,
          ),
        ),
      ),
    );
  }

  Widget _buildFilledCard(Widget cardChild) {
    return Container(
      margin: margin ?? EdgeInsets.all(AppDimensions.paddingSmall),
      child: Material(
        color: color ?? AppColors.surfaceVariant,
        elevation: elevation ?? AppDimensions.elevationNone,
        borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
        child: InkWell(
          onTap: onTap,
          borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
          child: Container(
            padding: padding ?? EdgeInsets.all(AppDimensions.paddingMedium),
            decoration: border != null
                ? BoxDecoration(
                    border: border,
                    borderRadius: borderRadius ?? BorderRadius.circular(AppDimensions.radiusMedium),
                  )
                : null,
            child: cardChild,
          ),
        ),
      ),
    );
  }
}

enum AppCardVariant {
  elevated,
  outlined,
  filled,
}

/// Specialized card for displaying statistics or metrics
class StatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final Widget? icon;
  final Color? valueColor;
  final VoidCallback? onTap;
  final String? trend;
  final bool isPositiveTrend;

  const StatCard({
    Key? key,
    required this.title,
    required this.value,
    this.subtitle,
    this.icon,
    this.valueColor,
    this.onTap,
    this.trend,
    this.isPositiveTrend = true,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard(
      onTap: onTap,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              if (icon != null) ...[
                Container(
                  padding: EdgeInsets.all(AppDimensions.paddingSmall),
                  decoration: BoxDecoration(
                    color: AppColors.primaryContainer,
                    borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                  ),
                  child: icon,
                ),
                SizedBox(width: AppDimensions.paddingMedium),
              ],
              Expanded(
                child: Text(
                  title,
                  style: AppTextStyles.cardSubtitle,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingMedium),
          Text(
            value,
            style: AppTextStyles.headlineSmall.copyWith(
              color: valueColor ?? AppColors.primary,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null || trend != null) ...[
            SizedBox(height: AppDimensions.paddingSmall),
            Row(
              children: [
                if (subtitle != null)
                  Expanded(
                    child: Text(
                      subtitle!,
                      style: AppTextStyles.bodySmall,
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                if (trend != null) ...[
                  Icon(
                    isPositiveTrend ? Icons.trending_up : Icons.trending_down,
                    size: AppDimensions.iconSmall,
                    color: isPositiveTrend ? AppColors.success : AppColors.error,
                  ),
                  SizedBox(width: AppDimensions.paddingTiny),
                  Text(
                    trend!,
                    style: AppTextStyles.bodySmall.copyWith(
                      color: isPositiveTrend ? AppColors.success : AppColors.error,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ],
        ],
      ),
    );
  }
}

/// Card for displaying list items with consistent styling
class ListCard extends StatelessWidget {
  final String title;
  final String? subtitle;
  final Widget? leading;
  final Widget? trailing;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final bool isSelected;

  const ListCard({
    Key? key,
    required this.title,
    this.subtitle,
    this.leading,
    this.trailing,
    this.onTap,
    this.onLongPress,
    this.isSelected = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return AppCard(
      onTap: onTap,
      color: isSelected ? AppColors.primaryContainer : null,
      child: InkWell(
        onLongPress: onLongPress,
        child: Row(
          children: [
            if (leading != null) ...[
              leading!,
              SizedBox(width: AppDimensions.paddingMedium),
            ],
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    title,
                    style: AppTextStyles.cardTitle.copyWith(
                      color: isSelected ? AppColors.onPrimaryContainer : null,
                    ),
                    maxLines: 2,
                    overflow: TextOverflow.ellipsis,
                  ),
                  if (subtitle != null) ...[
                    SizedBox(height: AppDimensions.paddingTiny),
                    Text(
                      subtitle!,
                      style: AppTextStyles.cardSubtitle.copyWith(
                        color: isSelected ? AppColors.onPrimaryContainer : null,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ],
                ],
              ),
            ),
            if (trailing != null) ...[
              SizedBox(width: AppDimensions.paddingMedium),
              trailing!,
            ],
          ],
        ),
      ),
    );
  }
}
