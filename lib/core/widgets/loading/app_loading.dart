import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Universal loading widget with different variants
class AppLoading extends StatelessWidget {
  final String? message;
  final AppLoadingSize size;
  final Color? color;
  final bool showMessage;

  const AppLoading({
    Key? key,
    this.message,
    this.size = AppLoadingSize.medium,
    this.color,
    this.showMessage = true,
  }) : super(key: key);

  const AppLoading.small({
    Key? key,
    this.message,
    this.color,
    this.showMessage = false,
  }) : size = AppLoadingSize.small,
       super(key: key);

  const AppLoading.large({
    Key? key,
    this.message,
    this.color,
    this.showMessage = true,
  }) : size = AppLoadingSize.large,
       super(key: key);

  @override
  Widget build(BuildContext context) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        SizedBox(
          width: _getSize(),
          height: _getSize(),
          child: CircularProgressIndicator(
            strokeWidth: _getStrokeWidth(),
            valueColor: AlwaysStoppedAnimation<Color>(
              color ?? AppColors.primary,
            ),
          ),
        ),
        if (showMessage && message != null) ...[
          SizedBox(height: AppDimensions.paddingMedium),
          Text(
            message!,
            style: _getTextStyle(),
            textAlign: TextAlign.center,
          ),
        ],
      ],
    );
  }

  double _getSize() {
    switch (size) {
      case AppLoadingSize.small:
        return 20.0;
      case AppLoadingSize.medium:
        return 32.0;
      case AppLoadingSize.large:
        return 48.0;
    }
  }

  double _getStrokeWidth() {
    switch (size) {
      case AppLoadingSize.small:
        return 2.0;
      case AppLoadingSize.medium:
        return 3.0;
      case AppLoadingSize.large:
        return 4.0;
    }
  }

  TextStyle _getTextStyle() {
    switch (size) {
      case AppLoadingSize.small:
        return AppTextStyles.bodySmall;
      case AppLoadingSize.medium:
        return AppTextStyles.bodyMedium;
      case AppLoadingSize.large:
        return AppTextStyles.bodyLarge;
    }
  }
}

enum AppLoadingSize {
  small,
  medium,
  large,
}

/// Full screen loading overlay
class LoadingOverlay extends StatelessWidget {
  final String? message;
  final bool isVisible;
  final Widget child;

  const LoadingOverlay({
    Key? key,
    required this.child,
    this.message,
    this.isVisible = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        child,
        if (isVisible)
          Container(
            color: Colors.black.withOpacity(0.5),
            child: Center(
              child: Container(
                padding: EdgeInsets.all(AppDimensions.paddingLarge),
                decoration: BoxDecoration(
                  color: AppColors.surface,
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                ),
                child: AppLoading.large(
                  message: message ?? 'Loading...',
                ),
              ),
            ),
          ),
      ],
    );
  }
}

/// Skeleton loading widget for list items
class SkeletonLoader extends StatefulWidget {
  final double? width;
  final double? height;
  final BorderRadius? borderRadius;

  const SkeletonLoader({
    Key? key,
    this.width,
    this.height,
    this.borderRadius,
  }) : super(key: key);

  @override
  State<SkeletonLoader> createState() => _SkeletonLoaderState();
}

class _SkeletonLoaderState extends State<SkeletonLoader>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _animation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 1500),
      vsync: this,
    );
    _animation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
    _animationController.repeat(reverse: true);
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _animation,
      builder: (context, child) {
        return Container(
          width: widget.width,
          height: widget.height ?? 16,
          decoration: BoxDecoration(
            borderRadius: widget.borderRadius ?? 
                BorderRadius.circular(AppDimensions.radiusSmall),
            gradient: LinearGradient(
              colors: [
                AppColors.surfaceVariant,
                AppColors.surfaceVariant.withOpacity(0.5),
                AppColors.surfaceVariant,
              ],
              stops: [
                0.0,
                _animation.value,
                1.0,
              ],
              begin: Alignment.centerLeft,
              end: Alignment.centerRight,
            ),
          ),
        );
      },
    );
  }
}

/// Skeleton loading for list items
class ListItemSkeleton extends StatelessWidget {
  final bool showAvatar;
  final bool showTrailing;

  const ListItemSkeleton({
    Key? key,
    this.showAvatar = true,
    this.showTrailing = false,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      child: Row(
        children: [
          if (showAvatar) ...[
            SkeletonLoader(
              width: AppDimensions.avatarMedium,
              height: AppDimensions.avatarMedium,
              borderRadius: BorderRadius.circular(AppDimensions.avatarMedium / 2),
            ),
            SizedBox(width: AppDimensions.paddingMedium),
          ],
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                SkeletonLoader(
                  width: double.infinity,
                  height: 16,
                ),
                SizedBox(height: AppDimensions.paddingSmall),
                SkeletonLoader(
                  width: MediaQuery.of(context).size.width * 0.6,
                  height: 14,
                ),
              ],
            ),
          ),
          if (showTrailing) ...[
            SizedBox(width: AppDimensions.paddingMedium),
            SkeletonLoader(
              width: 24,
              height: 24,
              borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
            ),
          ],
        ],
      ),
    );
  }
}

/// Skeleton loading for cards
class CardSkeleton extends StatelessWidget {
  final double? height;

  const CardSkeleton({
    Key? key,
    this.height,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      height: height ?? 120,
      margin: EdgeInsets.all(AppDimensions.paddingSmall),
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
        border: Border.all(color: AppColors.outline.withOpacity(0.2)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              SkeletonLoader(
                width: 40,
                height: 40,
                borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
              ),
              SizedBox(width: AppDimensions.paddingMedium),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    SkeletonLoader(
                      width: double.infinity,
                      height: 16,
                    ),
                    SizedBox(height: AppDimensions.paddingSmall),
                    SkeletonLoader(
                      width: MediaQuery.of(context).size.width * 0.4,
                      height: 14,
                    ),
                  ],
                ),
              ),
            ],
          ),
          SizedBox(height: AppDimensions.paddingMedium),
          SkeletonLoader(
            width: double.infinity,
            height: 12,
          ),
          SizedBox(height: AppDimensions.paddingSmall),
          SkeletonLoader(
            width: MediaQuery.of(context).size.width * 0.7,
            height: 12,
          ),
        ],
      ),
    );
  }
}
