import 'package:flutter/material.dart';
import '../../theme/app_colors.dart';
import '../../theme/app_dimensions.dart';
import '../../theme/app_text_styles.dart';

/// Professional bottom navigation bar with modern design
class AppBottomNavigation extends StatelessWidget {
  final int currentIndex;
  final Function(int) onTap;
  final List<AppBottomNavItem> items;

  const AppBottomNavigation({
    Key? key,
    required this.currentIndex,
    required this.onTap,
    required this.items,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppColors.getSurface(context),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.1),
            blurRadius: AppDimensions.shadowBlurMedium,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Container(
          height: AppDimensions.bottomNavHeight,
          padding: EdgeInsets.symmetric(horizontal: AppDimensions.paddingSmall),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceAround,
            children: items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value;
              final isSelected = index == currentIndex;
              
              return Expanded(
                child: InkWell(
                  onTap: () => onTap(index),
                  borderRadius: BorderRadius.circular(AppDimensions.radiusMedium),
                  child: Container(
                    padding: EdgeInsets.symmetric(
                      vertical: AppDimensions.paddingSmall,
                      horizontal: AppDimensions.paddingTiny,
                    ),
                    child: Column(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        AnimatedContainer(
                          duration: Duration(milliseconds: AppDimensions.animationDurationFast),
                          padding: EdgeInsets.all(AppDimensions.paddingTiny),
                          decoration: BoxDecoration(
                            color: isSelected 
                                ? AppColors.primaryContainer 
                                : Colors.transparent,
                            borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
                          ),
                          child: Icon(
                            isSelected ? item.activeIcon : item.icon,
                            color: isSelected 
                                ? AppColors.onPrimaryContainer 
                                : AppColors.onSurfaceVariant,
                            size: AppDimensions.iconMedium,
                          ),
                        ),
                        SizedBox(height: AppDimensions.paddingTiny),
                        Text(
                          item.label,
                          style: AppTextStyles.labelSmall.copyWith(
                            color: isSelected 
                                ? AppColors.primary 
                                : AppColors.onSurfaceVariant,
                            fontWeight: isSelected ? FontWeight.w600 : FontWeight.normal,
                          ),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ],
                    ),
                  ),
                ),
              );
            }).toList(),
          ),
        ),
      ),
    );
  }
}

/// Bottom navigation item model
class AppBottomNavItem {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final int? badgeCount;

  const AppBottomNavItem({
    required this.icon,
    required this.activeIcon,
    required this.label,
    this.badgeCount,
  });
}

/// Professional app drawer with modern design
class AppDrawer extends StatelessWidget {
  final String? userDisplayName;
  final String? userEmail;
  final String? userAvatarUrl;
  final List<AppDrawerItem> items;
  final VoidCallback? onProfileTap;

  const AppDrawer({
    Key? key,
    this.userDisplayName,
    this.userEmail,
    this.userAvatarUrl,
    required this.items,
    this.onProfileTap,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    return Drawer(
      backgroundColor: AppColors.getSurface(context),
      child: Column(
        children: [
          _buildHeader(context),
          Expanded(
            child: ListView(
              padding: EdgeInsets.zero,
              children: items.map((item) => _buildDrawerItem(context, item)).toList(),
            ),
          ),
          _buildFooter(context),
        ],
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      height: 200,
      decoration: BoxDecoration(
        gradient: AppColors.primaryGradient,
      ),
      child: SafeArea(
        child: Padding(
          padding: EdgeInsets.all(AppDimensions.paddingLarge),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              CircleAvatar(
                radius: 30,
                backgroundColor: AppColors.onPrimary.withOpacity(0.2),
                backgroundImage: userAvatarUrl != null 
                    ? NetworkImage(userAvatarUrl!) 
                    : null,
                child: userAvatarUrl == null 
                    ? Icon(
                        Icons.person,
                        size: AppDimensions.iconLarge,
                        color: AppColors.onPrimary,
                      )
                    : null,
              ),
              SizedBox(height: AppDimensions.paddingMedium),
              Text(
                userDisplayName ?? 'User',
                style: AppTextStyles.titleLarge.copyWith(
                  color: AppColors.onPrimary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              if (userEmail != null) ...[
                SizedBox(height: AppDimensions.paddingTiny),
                Text(
                  userEmail!,
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.onPrimary.withOpacity(0.8),
                  ),
                ),
              ],
              Spacer(),
              if (onProfileTap != null)
                TextButton.icon(
                  onPressed: onProfileTap,
                  icon: Icon(
                    Icons.edit,
                    color: AppColors.onPrimary,
                    size: AppDimensions.iconSmall,
                  ),
                  label: Text(
                    'Edit Profile',
                    style: AppTextStyles.labelMedium.copyWith(
                      color: AppColors.onPrimary,
                    ),
                  ),
                ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDrawerItem(BuildContext context, AppDrawerItem item) {
    return ListTile(
      leading: Container(
        padding: EdgeInsets.all(AppDimensions.paddingSmall),
        decoration: BoxDecoration(
          color: item.iconColor?.withOpacity(0.1) ?? AppColors.primaryContainer,
          borderRadius: BorderRadius.circular(AppDimensions.radiusSmall),
        ),
        child: Icon(
          item.icon,
          color: item.iconColor ?? AppColors.onPrimaryContainer,
          size: AppDimensions.iconMedium,
        ),
      ),
      title: Text(
        item.title,
        style: AppTextStyles.titleSmall.copyWith(
          fontWeight: FontWeight.w500,
        ),
      ),
      subtitle: item.subtitle != null 
          ? Text(
              item.subtitle!,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.onSurfaceVariant,
              ),
            )
          : null,
      trailing: item.trailing ?? (item.onTap != null 
          ? Icon(
              Icons.chevron_right,
              color: AppColors.onSurfaceVariant,
            )
          : null),
      onTap: item.onTap,
      contentPadding: EdgeInsets.symmetric(
        horizontal: AppDimensions.paddingLarge,
        vertical: AppDimensions.paddingTiny,
      ),
    );
  }

  Widget _buildFooter(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(AppDimensions.paddingMedium),
      decoration: BoxDecoration(
        border: Border(
          top: BorderSide(
            color: AppColors.outline.withOpacity(0.2),
            width: 1,
          ),
        ),
      ),
      child: Row(
        children: [
          Icon(
            Icons.info_outline,
            size: AppDimensions.iconSmall,
            color: AppColors.onSurfaceVariant,
          ),
          SizedBox(width: AppDimensions.paddingSmall),
          Text(
            'Sale 1 v1.0.0',
            style: AppTextStyles.bodySmall.copyWith(
              color: AppColors.onSurfaceVariant,
            ),
          ),
        ],
      ),
    );
  }
}

/// Drawer item model
class AppDrawerItem {
  final IconData icon;
  final String title;
  final String? subtitle;
  final Widget? trailing;
  final VoidCallback? onTap;
  final Color? iconColor;

  const AppDrawerItem({
    required this.icon,
    required this.title,
    this.subtitle,
    this.trailing,
    this.onTap,
    this.iconColor,
  });
}
