/// Consistent spacing and dimension system for the application
/// Following 8px grid system for consistent spacing
class AppDimensions {
  // Base unit (8px grid system)
  static const double baseUnit = 8.0;
  
  // Padding & Margins
  static const double paddingTiny = baseUnit * 0.5; // 4px
  static const double paddingSmall = baseUnit; // 8px
  static const double paddingMedium = baseUnit * 2; // 16px
  static const double paddingLarge = baseUnit * 3; // 24px
  static const double paddingXLarge = baseUnit * 4; // 32px
  static const double paddingXXLarge = baseUnit * 6; // 48px
  
  // <PERSON>gins (same as padding for consistency)
  static const double marginTiny = paddingTiny;
  static const double marginSmall = paddingSmall;
  static const double marginMedium = paddingMedium;
  static const double marginLarge = paddingLarge;
  static const double marginXLarge = paddingXLarge;
  static const double marginXXLarge = paddingXXLarge;
  
  // Border Radius
  static const double radiusSmall = 4.0;
  static const double radiusMedium = 8.0;
  static const double radiusLarge = 12.0;
  static const double radiusXLarge = 16.0;
  static const double radiusXXLarge = 24.0;
  static const double radiusCircular = 50.0;
  
  // Elevation
  static const double elevationNone = 0.0;
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
  static const double elevationXLarge = 12.0;
  
  // Icon Sizes
  static const double iconTiny = 16.0;
  static const double iconSmall = 20.0;
  static const double iconMedium = 24.0;
  static const double iconLarge = 32.0;
  static const double iconXLarge = 48.0;
  static const double iconXXLarge = 64.0;
  
  // Button Heights
  static const double buttonHeightSmall = 32.0;
  static const double buttonHeightMedium = 40.0;
  static const double buttonHeightLarge = 48.0;
  static const double buttonHeightXLarge = 56.0;
  
  // Input Field Heights
  static const double inputHeightSmall = 40.0;
  static const double inputHeightMedium = 48.0;
  static const double inputHeightLarge = 56.0;
  
  // Card Dimensions
  static const double cardMinHeight = 80.0;
  static const double cardMaxWidth = 400.0;
  static const double cardElevation = elevationSmall;
  
  // App Bar
  static const double appBarHeight = 56.0;
  static const double appBarElevation = elevationSmall;
  
  // Bottom Navigation
  static const double bottomNavHeight = 60.0;
  static const double bottomNavElevation = elevationMedium;
  
  // Drawer
  static const double drawerWidth = 280.0;
  
  // Dialog
  static const double dialogMaxWidth = 400.0;
  static const double dialogMinWidth = 280.0;
  static const double dialogBorderRadius = radiusLarge;
  
  // Snackbar
  static const double snackbarBorderRadius = radiusMedium;
  static const double snackbarElevation = elevationMedium;
  
  // List Items
  static const double listItemHeight = 56.0;
  static const double listItemDenseHeight = 48.0;
  static const double listItemPadding = paddingMedium;
  
  // Divider
  static const double dividerThickness = 1.0;
  static const double dividerIndent = paddingMedium;
  
  // Progress Indicators
  static const double progressIndicatorSize = 20.0;
  static const double progressIndicatorStrokeWidth = 2.0;
  
  // Avatar Sizes
  static const double avatarSmall = 32.0;
  static const double avatarMedium = 40.0;
  static const double avatarLarge = 56.0;
  static const double avatarXLarge = 72.0;
  
  // Chip Dimensions
  static const double chipHeight = 32.0;
  static const double chipBorderRadius = radiusLarge;
  
  // Tab Bar
  static const double tabBarHeight = 48.0;
  static const double tabIndicatorHeight = 2.0;
  
  // Search Bar
  static const double searchBarHeight = inputHeightMedium;
  static const double searchBarBorderRadius = radiusLarge;
  
  // Floating Action Button
  static const double fabSize = 56.0;
  static const double fabMiniSize = 40.0;
  static const double fabExtendedHeight = 48.0;
  
  // Grid Spacing
  static const double gridSpacing = paddingSmall;
  static const double gridRunSpacing = paddingSmall;
  
  // Form Spacing
  static const double formFieldSpacing = paddingMedium;
  static const double formSectionSpacing = paddingLarge;
  
  // Screen Padding
  static const double screenPaddingHorizontal = paddingMedium;
  static const double screenPaddingVertical = paddingMedium;
  
  // Breakpoints for Responsive Design
  static const double mobileBreakpoint = 600.0;
  static const double tabletBreakpoint = 900.0;
  static const double desktopBreakpoint = 1200.0;
  
  // Container Constraints
  static const double maxContentWidth = 1200.0;
  static const double minTouchTarget = 48.0;
  
  // Animation Durations (in milliseconds)
  static const int animationDurationFast = 150;
  static const int animationDurationMedium = 300;
  static const int animationDurationSlow = 500;
  
  // Border Widths
  static const double borderWidthThin = 1.0;
  static const double borderWidthMedium = 2.0;
  static const double borderWidthThick = 4.0;
  
  // Shadow Blur Radius
  static const double shadowBlurSmall = 4.0;
  static const double shadowBlurMedium = 8.0;
  static const double shadowBlurLarge = 16.0;
  
  // Z-Index Values
  static const int zIndexBase = 0;
  static const int zIndexDropdown = 1000;
  static const int zIndexModal = 2000;
  static const int zIndexTooltip = 3000;
  static const int zIndexToast = 4000;
  
  // Helper methods for responsive design
  static double getResponsivePadding(double screenWidth) {
    if (screenWidth < mobileBreakpoint) {
      return paddingMedium;
    } else if (screenWidth < tabletBreakpoint) {
      return paddingLarge;
    } else {
      return paddingXLarge;
    }
  }
  
  static double getResponsiveCardWidth(double screenWidth) {
    if (screenWidth < mobileBreakpoint) {
      return screenWidth - (paddingMedium * 2);
    } else if (screenWidth < tabletBreakpoint) {
      return (screenWidth - (paddingLarge * 3)) / 2;
    } else {
      return (screenWidth - (paddingXLarge * 4)) / 3;
    }
  }
  
  static int getGridCrossAxisCount(double screenWidth) {
    if (screenWidth < mobileBreakpoint) {
      return 1;
    } else if (screenWidth < tabletBreakpoint) {
      return 2;
    } else if (screenWidth < desktopBreakpoint) {
      return 3;
    } else {
      return 4;
    }
  }
}
