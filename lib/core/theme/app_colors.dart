import 'package:flutter/material.dart';

/// Professional color palette for the store management application
/// Following Material Design 3 color system with business-appropriate colors
class AppColors {
  // Primary Colors - Professional Blue
  static const Color primary = Color(0xFF1565C0); // Deep Blue
  static const Color primaryContainer = Color(0xFFE3F2FD);
  static const Color onPrimary = Color(0xFFFFFFFF);
  static const Color onPrimaryContainer = Color(0xFF0D47A1);
  
  // Secondary Colors - Complementary Orange/Amber
  static const Color secondary = Color(0xFFFF8F00); // Amber
  static const Color secondaryContainer = Color(0xFFFFF3E0);
  static const Color onSecondary = Color(0xFFFFFFFF);
  static const Color onSecondaryContainer = Color(0xFFE65100);
  
  // Tertiary Colors - Supporting Green
  static const Color tertiary = Color(0xFF2E7D32); // Green
  static const Color tertiaryContainer = Color(0xFFE8F5E8);
  static const Color onTertiary = Color(0xFFFFFFFF);
  static const Color onTertiaryContainer = Color(0xFF1B5E20);
  
  // Error Colors
  static const Color error = Color(0xFFD32F2F);
  static const Color errorContainer = Color(0xFFFFEBEE);
  static const Color onError = Color(0xFFFFFFFF);
  static const Color onErrorContainer = Color(0xFFB71C1C);
  
  // Warning Colors
  static const Color warning = Color(0xFFF57C00);
  static const Color warningContainer = Color(0xFFFFF8E1);
  static const Color onWarning = Color(0xFFFFFFFF);
  static const Color onWarningContainer = Color(0xFFE65100);
  
  // Success Colors
  static const Color success = Color(0xFF388E3C);
  static const Color successContainer = Color(0xFFE8F5E8);
  static const Color onSuccess = Color(0xFFFFFFFF);
  static const Color onSuccessContainer = Color(0xFF1B5E20);
  
  // Info Colors
  static const Color info = Color(0xFF0288D1);
  static const Color infoContainer = Color(0xFFE1F5FE);
  static const Color onInfo = Color(0xFFFFFFFF);
  static const Color onInfoContainer = Color(0xFF01579B);
  
  // Surface Colors - Light Theme
  static const Color surface = Color(0xFFFFFBFE);
  static const Color surfaceVariant = Color(0xFFF4F4F4);
  static const Color onSurface = Color(0xFF1C1B1F);
  static const Color onSurfaceVariant = Color(0xFF49454F);
  
  // Background Colors - Light Theme
  static const Color background = Color(0xFFFFFBFE);
  static const Color onBackground = Color(0xFF1C1B1F);
  
  // Outline Colors
  static const Color outline = Color(0xFF79747E);
  static const Color outlineVariant = Color(0xFFCAC4D0);
  
  // Surface Colors - Dark Theme
  static const Color surfaceDark = Color(0xFF1C1B1F);
  static const Color surfaceVariantDark = Color(0xFF49454F);
  static const Color onSurfaceDark = Color(0xFFE6E1E5);
  static const Color onSurfaceVariantDark = Color(0xFFCAC4D0);
  
  // Background Colors - Dark Theme
  static const Color backgroundDark = Color(0xFF1C1B1F);
  static const Color onBackgroundDark = Color(0xFFE6E1E5);
  
  // Outline Colors - Dark Theme
  static const Color outlineDark = Color(0xFF938F99);
  static const Color outlineVariantDark = Color(0xFF49454F);
  
  // Shadow Colors
  static const Color shadow = Color(0xFF000000);
  static const Color scrim = Color(0xFF000000);
  
  // Inverse Colors
  static const Color inverseSurface = Color(0xFF313033);
  static const Color onInverseSurface = Color(0xFFF4EFF4);
  static const Color inversePrimary = Color(0xFF90CAF9);
  
  // Legacy Colors (for backward compatibility)
  static const Color cBlack = Color(0xFF131313);
  static const Color cWhite = Color(0xFFFFFFFF);
  static const Color cBlue = Color(0xFF048BD7);
  static const Color cGray = Color(0xFF8D8D8E);
  static const Color cRed = Color(0xFFCB0000);
  static const Color cYellow = Color(0xFFE28A29);
  static const Color cAppBarColor = Color(0xFF032B4B);
  static const Color cBackgroundColor = Color(0xFF212121);
  static const Color cItemColor = Color(0xFF424242);
  static const Color cButtonColor = Color(0xFFEDB531);
  static const Color cRedDarkColor = Color(0xFF720202);
  
  // Gradient Colors
  static const LinearGradient primaryGradient = LinearGradient(
    colors: [primary, Color(0xFF1976D2)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  static const LinearGradient secondaryGradient = LinearGradient(
    colors: [secondary, Color(0xFFFFA000)],
    begin: Alignment.topLeft,
    end: Alignment.bottomRight,
  );
  
  // Status Colors for Business Logic
  static const Color statusActive = success;
  static const Color statusInactive = Color(0xFF9E9E9E);
  static const Color statusPending = warning;
  static const Color statusCancelled = error;
  static const Color statusCompleted = success;
  
  // Chart Colors
  static const List<Color> chartColors = [
    primary,
    secondary,
    tertiary,
    warning,
    info,
    Color(0xFF9C27B0), // Purple
    Color(0xFF607D8B), // Blue Grey
    Color(0xFF795548), // Brown
  ];
  
  // Opacity Variants
  static Color primaryWithOpacity(double opacity) => primary.withOpacity(opacity);
  static Color secondaryWithOpacity(double opacity) => secondary.withOpacity(opacity);
  static Color errorWithOpacity(double opacity) => error.withOpacity(opacity);
  static Color successWithOpacity(double opacity) => success.withOpacity(opacity);
  static Color warningWithOpacity(double opacity) => warning.withOpacity(opacity);
  static Color infoWithOpacity(double opacity) => info.withOpacity(opacity);
  
  // Helper methods for theme-aware colors
  static Color getOnSurface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? onSurfaceDark 
        : onSurface;
  }
  
  static Color getSurface(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? surfaceDark 
        : surface;
  }
  
  static Color getBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? backgroundDark 
        : background;
  }
  
  static Color getOnBackground(BuildContext context) {
    return Theme.of(context).brightness == Brightness.dark 
        ? onBackgroundDark 
        : onBackground;
  }
}
