import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/core/util/app_constants.dart';
import 'package:market/function/function.dart';
import 'package:pattern_formatter/numeric_formatter.dart';

class ProductAddPage extends StatefulWidget {
  final CollectionReference product;
  final Map<String, dynamic>? data;

  const ProductAddPage({
    super.key,
    required this.product,
    this.data,
  });

  @override
  State<ProductAddPage> createState() => _ProductAddPageState();
}

class _ProductAddPageState extends State<ProductAddPage> {
  TextEditingController nameController = TextEditingController();
  TextEditingController priceController = TextEditingController();
  TextEditingController countController = TextEditingController();
  List<ProductItem> listItem = <ProductItem>[];
  ProductItem? productItem;
  String? productName;

  @override
  void initState() {
    super.initState();
    fetchComplexData();
  }

  fetchComplexData() async {
    QuerySnapshot snapshot = await widget.product.get();
    if (snapshot.docs.isNotEmpty) {
      for (int i = 0; i < snapshot.docs.length; i++) {
        print(snapshot.docs[i]['name']);
        listItem
            .add(ProductItem.fromJson(snapshot.docs[i], snapshot.docs[i].id));
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cAppBarColor,
              cBackgroundColor,
              cBackgroundColor.withOpacity(0.8),
            ],
          ),
        ),
        child: SafeArea(
          child: Column(
            children: [
              _buildModernAppBar(context),
              Expanded(
                child: Container(
                  margin: const EdgeInsets.only(top: 8),
                  decoration: BoxDecoration(
                    color: cBackgroundColor.withOpacity(0.3),
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(24),
                      topRight: Radius.circular(24),
                    ),
                  ),
                  child: _buildBody(context),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildModernAppBar(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          GestureDetector(
            onTap: () => Navigator.pop(context),
            child: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: cItemColor.withOpacity(0.6),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: cButtonColor.withOpacity(0.3),
                  width: 1,
                ),
              ),
              child: const Icon(
                Icons.arrow_back_ios,
                color: cWhite,
                size: 20,
              ),
            ),
          ),
          const SizedBox(width: 16),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Mahsulot qo\'shish',
                  style: TextStyle(
                    color: cWhite,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Yangi mahsulot ma\'lumotlarini kiriting',
                  style: TextStyle(
                    color: cGray,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildBody(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Mahsulot ma\'lumotlari'),
          const SizedBox(height: 16),
          _buildModernAutocomplete(),
          const SizedBox(height: 20),
          _buildModernTextField(
            controller: priceController,
            label: 'Mahsulot sotilish narxi',
            hint: 'Narxni kiriting',
            icon: Icons.payments_outlined,
            keyboardType: TextInputType.number,
            inputFormatters: [ThousandsFormatter()],
            suffix: 'UZS',
          ),
          const SizedBox(height: 20),
          _buildModernTextField(
            controller: countController,
            label: 'Mahsulot soni',
            hint: 'Sonini kiriting',
            icon: Icons.inventory_2_outlined,
            keyboardType: TextInputType.number,
            inputFormatters: [ThousandsFormatter()],
            suffix: 'dona',
          ),
          const SizedBox(height: 32),
          _buildModernButton(),
        ],
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Text(
      title,
      style: TextStyle(
        color: cWhite,
        fontSize: 18,
        fontWeight: FontWeight.bold,
      ),
    );
  }

  Widget _buildModernAutocomplete() {
    return Container(
      decoration: BoxDecoration(
        color: cItemColor.withOpacity(0.6),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: cButtonColor.withOpacity(0.3),
          width: 1,
        ),
      ),
      child: Autocomplete<ProductItem>(
        optionsBuilder: (TextEditingValue textEditingValue) {
          if (textEditingValue.text == "") {
            priceController.clear();
            return const Iterable<ProductItem>.empty();
          }
          productName = textEditingValue.text;
          return listItem.where((ProductItem item) {
            return item.name.toLowerCase().contains(textEditingValue.text.toLowerCase());
          });
        },
        onSelected: (ProductItem element) {
          setState(() {
            productItem = element;
            priceController = TextEditingController(
              text: NumberFormat("#,###", "en_US").format(element.price),
            );
          });
        },
        fieldViewBuilder: (BuildContext context,
            TextEditingController textEditingController,
            FocusNode focusNode,
            VoidCallback onFieldSubmitted) {
          return TextField(
            controller: textEditingController,
            focusNode: focusNode,
            style: TextStyle(color: cWhite),
            decoration: InputDecoration(
              hintText: "Mahsulot nomini kiriting",
              hintStyle: TextStyle(color: cGray),
              prefixIcon: Icon(
                Icons.search,
                color: cButtonColor,
              ),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
            ),
            onSubmitted: (String value) {
              onFieldSubmitted();
            },
          );
        },
        optionsViewBuilder: (BuildContext context, AutocompleteOnSelected<ProductItem> onSelected, Iterable<ProductItem> options) {
          return Align(
            alignment: Alignment.topLeft,
            child: Material(
              color: cItemColor,
              borderRadius: BorderRadius.circular(12),
              elevation: 8,
              child: Container(
                width: MediaQuery.of(context).size.width - 48,
                constraints: BoxConstraints(maxHeight: 200),
                child: ListView.builder(
                  padding: EdgeInsets.zero,
                  shrinkWrap: true,
                  itemCount: options.length,
                  itemBuilder: (BuildContext context, int index) {
                    final ProductItem option = options.elementAt(index);
                    return InkWell(
                      onTap: () => onSelected(option),
                      child: Container(
                        padding: EdgeInsets.all(16),
                        decoration: BoxDecoration(
                          border: Border(
                            bottom: BorderSide(
                              color: cGray.withOpacity(0.2),
                              width: 1,
                            ),
                          ),
                        ),
                        child: Row(
                          children: [
                            Icon(
                              Icons.inventory_2_outlined,
                              color: cButtonColor,
                              size: 16,
                            ),
                            SizedBox(width: 12),
                            Expanded(
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Text(
                                    option.name,
                                    style: TextStyle(
                                      color: cWhite,
                                      fontWeight: FontWeight.w500,
                                    ),
                                  ),
                                  Text(
                                    '${NumberFormat("#,###", "en_US").format(option.price)} UZS',
                                    style: TextStyle(
                                      color: cGray,
                                      fontSize: 12,
                                    ),
                                  ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildModernTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    required IconData icon,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? suffix,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Icon(
              icon,
              color: cButtonColor,
              size: 16,
            ),
            SizedBox(width: 8),
            Text(
              label,
              style: TextStyle(
                color: cWhite,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
        SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: cItemColor.withOpacity(0.6),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: cButtonColor.withOpacity(0.3),
              width: 1,
            ),
          ),
          child: TextField(
            controller: controller,
            style: TextStyle(color: cWhite),
            keyboardType: keyboardType,
            inputFormatters: inputFormatters,
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(color: cGray),
              border: InputBorder.none,
              contentPadding: EdgeInsets.all(16),
              suffixText: suffix,
              suffixStyle: TextStyle(
                color: cGray,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildModernButton() {
    return GestureDetector(
      onTap: () async {
        bool result = await InternetConnectionChecker().hasConnection;
        if (result == true) {
          try {
            if (productItem != null) {
              int totalCount = productItem!.count +
                  int.parse(countController.text.replaceAll(',', ''));
              widget.product.doc(productItem!.id).update({
                "name": productItem?.name,
                "price": int.parse(priceController.text.replaceAll(',', '')),
                "count": totalCount
              });
            } else {
              widget.product.add({
                "name": productName,
                "price": int.parse(priceController.text.replaceAll(',', '')),
                "count": int.parse(countController.text.replaceAll(',', '')),
              });
            }
            Navigator.pop(context);
          } catch (e) {
            showToast(e.toString());
          }
        } else {
          showToast("Internet yo'q");
        }
      },
      child: Container(
        width: double.infinity,
        height: 56,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              cButtonColor,
              cButtonColor.withOpacity(0.8),
            ],
          ),
          borderRadius: BorderRadius.circular(16),
          boxShadow: [
            BoxShadow(
              color: cButtonColor.withOpacity(0.3),
              blurRadius: 15,
              offset: const Offset(0, 5),
            ),
          ],
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.save_outlined,
              color: cBlack,
              size: 20,
            ),
            SizedBox(width: 8),
            Text(
              widget.data == null ? "Saqlash" : 'O\'zgartirish',
              style: TextStyle(
                color: cBlack,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}

setSearchParam(String caseNumber) {
  List<String> caseSearchList = [];
  String temp = "";
  for (int i = 0; i < caseNumber.length; i++) {
    temp = temp + caseNumber[i];
    caseSearchList.add(temp);
  }
  return caseSearchList;
}

class TestItem {
  final String label;
  dynamic value;

  TestItem({required this.label, this.value});

  factory TestItem.fromJson(Map<String, dynamic> json) {
    return TestItem(label: json['label'], value: json['value']);
  }

  @override
  String toString() {
    return 'TestItem{label: $label, value: $value}';
  }
}

class ProductItem {
  String id;
  int count;
  String name;
  int price;

  ProductItem(
      {required this.id,
      required this.count,
      required this.name,
      required this.price});

  factory ProductItem.fromJson(QueryDocumentSnapshot json, String id) {
    return ProductItem(
        id: id, count: json['count'], name: json['name'], price: json['price']);
  }

  @override
  String toString() {
    return '$name';
  }
}
