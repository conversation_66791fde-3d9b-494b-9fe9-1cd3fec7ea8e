import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';
import 'package:flutter_slidable/flutter_slidable.dart';
import 'package:internet_connection_checker/internet_connection_checker.dart';
import 'package:intl/intl.dart';
import 'package:market/function/function.dart';

import 'check_detail.dart';
import 'loan_add_page.dart';

class LoanPage extends StatefulWidget {
  const LoanPage({super.key});

  @override
  State<LoanPage> createState() => _LoanPageState();
}

class _LoanPageState extends State<LoanPage> {
  final DateFormat formatterDate = DateFormat('dd-MM-yyyy');

  TextEditingController searchController = TextEditingController();
  final CollectionReference loan =
      FirebaseFirestore.instance.collection("loan");

  Stream<QuerySnapshot> searchFirestoreRealTime(String searchText) {
    if (searchText.isEmpty) {
      return FirebaseFirestore.instance.collection('loan').snapshots();
    } else {
      return FirebaseFirestore.instance
          .collection('loan')
          .where('name', arrayContains: searchText)
          .snapshots();
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text("Nasiya Savdo"),
      ),
      body: Column(
        children: [
          Padding(
            padding: const EdgeInsets.all(8.0),
            child: TextField(
              controller: searchController,
              decoration: InputDecoration(
                  border: OutlineInputBorder(), hintText: "Search"),
              onChanged: (value) {
                setState(() {});
              },
            ),
          ),
          Expanded(
            child: StreamBuilder(
                stream: searchFirestoreRealTime(searchController.text),
                builder: (context, snapshot) {
                  print(snapshot.connectionState);
                  if (snapshot.connectionState == ConnectionState.waiting) {
                    return Center(
                      child: CircularProgressIndicator(),
                    );
                  } else if (snapshot.connectionState == ConnectionState.done) {
                    return ListView.builder(
                        itemCount: snapshot.data?.docs.length,
                        itemBuilder: (BuildContext context, int index) {
                          final DocumentSnapshot documentSnapshot =
                              snapshot.data!.docs[index];
                          List<dynamic> names = documentSnapshot['name'];
                          return InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                  context: context,
                                  builder: (context) {
                                    return LoanAddPage(
                                      documentSnapshot: documentSnapshot,
                                    );
                                  });
                            },
                            child: Card(
                              child: ListTile(
                                  title: Text(names.last),
                                  trailing: Text(NumberFormat("#,###", "en_US")
                                      .format(documentSnapshot['loan']))),
                            ),
                          );
                        });
                  } else if (snapshot.connectionState ==
                      ConnectionState.active) {
                    return ListView.builder(
                        itemCount: snapshot.data?.docs.length,
                        itemBuilder: (BuildContext context, int index) {
                          final DocumentSnapshot documentSnapshot =
                              snapshot.data!.docs[index];
                          List<dynamic> names = documentSnapshot['name'];

                          DateTime loanTime =
                              DateTime.parse(documentSnapshot['date']);

                          DateTime now = DateTime.now();
                          bool loanColor =
                              now.subtract(const Duration(days: 7)).isAfter(loanTime);
                          return InkWell(
                            onTap: () {
                              showModalBottomSheet(
                                  context: context,
                                  builder: (context) {
                                    return LoanAddPage(
                                      documentSnapshot: documentSnapshot,
                                    );
                                  });
                            },
                            child: Slidable(
                              key: const ValueKey(0),
                              endActionPane: ActionPane(
                                  motion: const StretchMotion(),
                                  children: [
                                    SlidableAction(
                                      onPressed: (BuildContext context) {
                                        showDialog(
                                            context: context,
                                            builder: (context) {
                                              return AlertDialog(
                                                title:
                                                    const Text("Ogohlantirish"),
                                                content: const Text(
                                                    "Rostan ham ushbu mahsulotni o'chirib tashlamoqchimisiz?"),
                                                actions: [
                                                  TextButton(
                                                      onPressed: () {
                                                        print(DateTime.parse(
                                                                documentSnapshot[
                                                                    'date'])
                                                            .microsecondsSinceEpoch);
                                                        Navigator.pop(context);
                                                      },
                                                      child:
                                                          const Text("Yo'q")),
                                                  TextButton(
                                                      onPressed: () {
                                                        loan
                                                            .doc(
                                                                documentSnapshot
                                                                    .id)
                                                            .delete();
                                                        Navigator.pop(context);
                                                      },
                                                      child: const Text("Ha")),
                                                ],
                                              );
                                            });
                                      },
                                      icon: Icons.delete,
                                      backgroundColor: Colors.red,

                                    ),
                                  ]),
                              child: Card(
                                child: Container(
                                  color: loanColor?Colors.red[100]:Colors.white,
                                  padding: EdgeInsets.all(10),
                                  alignment: Alignment.center,
                                  child: Column(
                                    mainAxisAlignment: MainAxisAlignment.center,
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          Text(names.isNotEmpty?capitalFirst(names.last):"empty"),
                                          Text(NumberFormat("#,###", "en_US")
                                              .format(documentSnapshot['loan']))
                                        ],
                                      ),
                                      SizedBox(
                                        height: 5,
                                      ),
                                      Text(documentSnapshot['date'].toString())
                                    ],
                                  ),
                                ),
                              ),
                            ),
                          );
                        });
                  } else {
                    return Text("Error");
                  }
                }),
          )
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () async {
          bool result = await InternetConnectionChecker().hasConnection;
          if (result == true) {
            showModalBottomSheet(
                context: context,
                builder: (context) {
                  return LoanAddPage();
                });
          } else {
            showToast("Internet yo'q");
          }
        },
        child: Icon(Icons.add),
      ),
    );
  }
}
String capitalFirst(String txt){
 return txt[0].toUpperCase() + txt.substring(1);}